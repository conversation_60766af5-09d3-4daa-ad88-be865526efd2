#!/usr/bin/env python3
"""
Minimal Flask server for the stock prediction website.
"""

from flask import Flask, jsonify, request
from flask_cors import CORS
import os

# Create Flask app
app = Flask(__name__)
CORS(app)  # Enable CORS for all routes

# Basic routes
@app.route('/')
def home():
    return jsonify({
        'message': 'Stock Prediction API is running!',
        'status': 'success',
        'endpoints': [
            '/api/health',
            '/api/auth/register',
            '/api/auth/login'
        ]
    })

@app.route('/api/health')
def health():
    return jsonify({
        'status': 'ok',
        'message': 'Server is running',
        'version': '1.0.0'
    })

@app.route('/api/auth/register', methods=['POST'])
def register():
    return jsonify({
        'message': 'Registration endpoint (demo)',
        'status': 'success',
        'note': 'This is a minimal demo server. Full functionality requires the complete backend.'
    })

@app.route('/api/auth/login', methods=['POST'])
def login():
    return jsonify({
        'message': 'Login endpoint (demo)',
        'status': 'success',
        'note': 'This is a minimal demo server. Full functionality requires the complete backend.'
    })

if __name__ == '__main__':
    print("🚀 Starting Minimal Stock Prediction Server...")
    print("🌐 Server will be available at: http://127.0.0.1:5000")
    print("📁 Frontend is already open in your browser")
    print("💡 This is a minimal server for demonstration")
    print("🔧 For full functionality, use the complete backend")
    print("\n" + "="*50)
    
    app.run(
        host='127.0.0.1',
        port=5000,
        debug=True,
        use_reloader=False
    )
