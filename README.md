# Stock Prediction Website

A machine learning-powered web application for stock price prediction with user authentication and personalized preferences.

## Features

- **User Authentication**: Secure login/logout system with JWT tokens
- **Theme Preferences**: Dark/Light mode toggle with user preference storage
- **Stock Prediction**: ML-based stock price movement prediction
- **Responsive Design**: Mobile-friendly interface

## Project Structure

```
stock-prediction-website/
├── backend/                 # Flask backend API
│   ├── app/
│   │   ├── models/         # Database models
│   │   ├── routes/         # API endpoints
│   │   ├── utils/          # Utility functions
│   │   └── __init__.py
│   ├── ml_models/          # Machine learning models
│   ├── config.py           # Configuration settings
│   └── run.py              # Application entry point
├── frontend/               # Frontend web interface
│   ├── css/               # Stylesheets
│   ├── js/                # JavaScript files
│   ├── pages/             # HTML pages
│   └── assets/            # Images and other assets
├── data/                  # Data storage
├── tests/                 # Test files
├── requirements.txt       # Python dependencies
└── README.md             # Project documentation
```

## Quick Start

### Option 1: Easy Setup (Recommended)
```bash
# 1. Install dependencies
pip install -r requirements.txt

# 2. Start both backend and frontend servers
python start_server.py
```

The application will automatically:
- Start the backend API server on http://127.0.0.1:5000
- Start the frontend server on http://127.0.0.1:3000
- Open your browser to the login page
- Initialize the database with default settings

### Option 2: Manual Setup

1. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

2. **Set up environment variables (optional)**
   ```bash
   cp .env.example .env
   # Edit .env with your configuration if needed
   ```

3. **Initialize database**
   ```bash
   python backend/run.py init-db
   ```

4. **Train ML model (optional)**
   ```bash
   python train_model.py
   ```

5. **Start backend server**
   ```bash
   python backend/run.py
   ```

6. **Start frontend server (in another terminal)**
   ```bash
   cd frontend
   python -m http.server 3000
   ```

7. **Open your browser**
   ```
   http://127.0.0.1:3000
   ```

## Testing the Setup

Run the test script to verify everything is working:
```bash
python test_setup.py
```

This will test:
- Backend server connectivity
- User registration and login
- Theme preferences
- Stock prediction functionality
- Frontend file availability

## API Endpoints

### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/profile` - Get user profile

### User Preferences
- `GET /api/user/preferences` - Get user preferences
- `PUT /api/user/preferences` - Update user preferences

### Stock Prediction
- `POST /api/predict` - Get stock prediction
- `GET /api/stocks/search` - Search for stocks

## Features Implemented

### ✅ User Authentication
- Secure user registration with email validation
- Login/logout with JWT tokens
- Password strength requirements
- User profile management

### ✅ Theme System
- Dark/Light mode toggle
- User preference persistence
- Smooth theme transitions
- System theme detection

### ✅ Stock Prediction
- AI-powered stock movement prediction
- Real-time stock data integration
- Confidence scoring
- Prediction history tracking

### ✅ Dashboard
- User statistics and overview
- Recent predictions display
- Quick action buttons
- Responsive design

### ✅ Settings Management
- Profile information updates
- Theme preferences
- Notification settings
- Account management

## Technologies Used

- **Backend**: Flask, SQLAlchemy, JWT, Flask-CORS, Bcrypt
- **Frontend**: HTML5, CSS3, JavaScript (ES6+), Font Awesome
- **Database**: SQLite (development), PostgreSQL (production ready)
- **ML**: scikit-learn, pandas, numpy, Random Forest
- **Data Source**: Yahoo Finance API (yfinance)
- **Development**: Python 3.7+, Virtual Environment

## Usage Guide

### First Time Setup
1. Run `python start_server.py` to start the application
2. Register a new account on the login page
3. Explore the dashboard to see your account overview
4. Use the "Predict" section to make stock predictions
5. Check your prediction history in the "History" section
6. Customize your experience in "Settings"

### Making Predictions
1. Go to the "Predict" section
2. Enter a stock symbol (e.g., AAPL, GOOGL, TSLA)
3. Select a future date for prediction
4. Click "Generate Prediction"
5. View the AI-generated prediction with confidence score

### Customizing Themes
- Click the theme toggle button (moon/sun icon) in the top navigation
- Or go to Settings > Appearance to choose your preferred theme
- Your preference is automatically saved and synced across devices

## Troubleshooting

### Common Issues

**Server won't start:**
- Make sure all dependencies are installed: `pip install -r requirements.txt`
- Check if ports 5000 and 3000 are available
- Try running `python backend/run.py` directly to see error messages

**Prediction errors:**
- The ML model needs internet access to fetch stock data
- Some stock symbols might not be available
- Try training the model manually: `python train_model.py`

**Theme not saving:**
- Make sure you're logged in
- Check browser console for JavaScript errors
- Clear browser cache and try again

### Getting Help
- Check the console output for error messages
- Run `python test_setup.py` to diagnose issues
- Ensure all required files are present in the project directory

## Development

### Project Structure
```
stock-prediction-website/
├── backend/                 # Flask backend
│   ├── app/                # Application code
│   ├── ml_models/          # ML model code
│   └── run.py              # Server entry point
├── frontend/               # Web interface
│   ├── css/               # Stylesheets
│   ├── js/                # JavaScript
│   └── *.html             # HTML pages
├── requirements.txt        # Dependencies
├── start_server.py        # Easy startup script
├── test_setup.py          # Testing script
└── train_model.py         # ML model training
```

### Adding New Features
1. Backend: Add routes in `backend/app/routes/`
2. Frontend: Update HTML, CSS, and JS files
3. Database: Add models in `backend/app/models/`
4. ML: Enhance the model in `backend/ml_models/`

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Yahoo Finance for stock data
- Font Awesome for icons
- Flask and scikit-learn communities for excellent documentation
