// Theme Management JavaScript

// Theme configuration
const THEMES = {
    LIGHT: 'light',
    DARK: 'dark'
};

const STORAGE_KEY = 'user_theme_preference';
const API_BASE_URL = 'http://127.0.0.1:5000/api';

// Initialize theme system
document.addEventListener('DOMContentLoaded', function() {
    initializeTheme();
    setupThemeToggle();
});

// Initialize theme on page load
function initializeTheme() {
    // Get theme preference from various sources (priority order)
    let theme = getThemePreference();
    
    // Apply the theme
    applyTheme(theme);
    
    // Update toggle button
    updateThemeToggleIcon(theme);
    
    // Sync with server if user is logged in
    syncThemeWithServer();
}

// Get theme preference from multiple sources
function getThemePreference() {
    // 1. Check localStorage first
    let theme = localStorage.getItem(STORAGE_KEY);
    
    // 2. Check user data if logged in
    if (!theme) {
        const userData = getUserData();
        if (userData && userData.preferences) {
            theme = userData.preferences.theme;
        }
    }
    
    // 3. Check system preference
    if (!theme) {
        theme = window.matchMedia('(prefers-color-scheme: dark)').matches 
            ? THEMES.DARK 
            : THEMES.LIGHT;
    }
    
    // 4. Default to light theme
    return theme || THEMES.LIGHT;
}

// Apply theme to the document
function applyTheme(theme) {
    const validTheme = Object.values(THEMES).includes(theme) ? theme : THEMES.LIGHT;
    
    // Set data attribute on document element
    document.documentElement.setAttribute('data-theme', validTheme);
    
    // Store in localStorage
    localStorage.setItem(STORAGE_KEY, validTheme);
    
    // Add transition class for smooth theme switching
    document.body.classList.add('theme-transition');
    
    // Remove transition class after animation
    setTimeout(() => {
        document.body.classList.remove('theme-transition');
    }, 300);
    
    // Dispatch custom event for other components
    window.dispatchEvent(new CustomEvent('themeChanged', { 
        detail: { theme: validTheme } 
    }));
}

// Setup theme toggle button
function setupThemeToggle() {
    const themeToggle = document.getElementById('themeToggle');
    
    if (themeToggle) {
        themeToggle.addEventListener('click', toggleTheme);
        
        // Add keyboard support
        themeToggle.addEventListener('keydown', function(event) {
            if (event.key === 'Enter' || event.key === ' ') {
                event.preventDefault();
                toggleTheme();
            }
        });
    }
    
    // Listen for system theme changes
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', function(e) {
        // Only auto-switch if user hasn't manually set a preference
        const hasManualPreference = localStorage.getItem(STORAGE_KEY) || 
                                   (getUserData() && getUserData().preferences);
        
        if (!hasManualPreference) {
            const newTheme = e.matches ? THEMES.DARK : THEMES.LIGHT;
            applyTheme(newTheme);
            updateThemeToggleIcon(newTheme);
        }
    });
}

// Toggle between light and dark themes
function toggleTheme() {
    const currentTheme = document.documentElement.getAttribute('data-theme');
    const newTheme = currentTheme === THEMES.DARK ? THEMES.LIGHT : THEMES.DARK;
    
    // Apply new theme
    applyTheme(newTheme);
    updateThemeToggleIcon(newTheme);
    
    // Update server if user is logged in
    updateThemeOnServer(newTheme);
    
    // Show feedback
    showThemeChangeNotification(newTheme);
}

// Update theme toggle icon
function updateThemeToggleIcon(theme) {
    const themeToggle = document.getElementById('themeToggle');
    
    if (themeToggle) {
        const icon = themeToggle.querySelector('i');
        
        if (icon) {
            // Remove existing classes
            icon.classList.remove('fa-moon', 'fa-sun');
            
            // Add appropriate icon
            if (theme === THEMES.DARK) {
                icon.classList.add('fa-sun');
                themeToggle.setAttribute('title', 'Switch to light mode');
            } else {
                icon.classList.add('fa-moon');
                themeToggle.setAttribute('title', 'Switch to dark mode');
            }
        }
    }
}

// Get user data from localStorage
function getUserData() {
    try {
        const userData = localStorage.getItem('user_data');
        return userData ? JSON.parse(userData) : null;
    } catch (error) {
        console.error('Error parsing user data:', error);
        return null;
    }
}

// Get access token
function getAccessToken() {
    return localStorage.getItem('access_token');
}

// Sync theme with server on page load
async function syncThemeWithServer() {
    const token = getAccessToken();
    
    if (!token) {
        return; // User not logged in
    }
    
    try {
        const response = await fetch(`${API_BASE_URL}/user/preferences`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            const serverTheme = data.preferences?.theme;
            
            if (serverTheme && serverTheme !== getCurrentTheme()) {
                // Server has different theme, apply it
                applyTheme(serverTheme);
                updateThemeToggleIcon(serverTheme);
            }
        }
    } catch (error) {
        console.error('Error syncing theme with server:', error);
    }
}

// Update theme preference on server
async function updateThemeOnServer(theme) {
    const token = getAccessToken();
    
    if (!token) {
        return; // User not logged in
    }
    
    try {
        const response = await fetch(`${API_BASE_URL}/user/preferences/theme`, {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ theme })
        });
        
        if (response.ok) {
            console.log('Theme preference updated on server');
        } else {
            console.error('Failed to update theme on server');
        }
    } catch (error) {
        console.error('Error updating theme on server:', error);
    }
}

// Get current theme
function getCurrentTheme() {
    return document.documentElement.getAttribute('data-theme') || THEMES.LIGHT;
}

// Show theme change notification
function showThemeChangeNotification(theme) {
    // Only show notification if the showNotification function exists
    if (typeof showNotification === 'function') {
        const message = theme === THEMES.DARK 
            ? 'Switched to dark mode' 
            : 'Switched to light mode';
        
        showNotification(message, 'info');
    }
}

// Export functions for use in other modules
window.ThemeManager = {
    getCurrentTheme,
    applyTheme,
    toggleTheme,
    THEMES
};

// Add CSS for smooth theme transitions
const style = document.createElement('style');
style.textContent = `
    .theme-transition,
    .theme-transition *,
    .theme-transition *:before,
    .theme-transition *:after {
        transition: background-color 0.3s ease-in-out, 
                   color 0.3s ease-in-out, 
                   border-color 0.3s ease-in-out,
                   box-shadow 0.3s ease-in-out !important;
    }
`;
document.head.appendChild(style);
