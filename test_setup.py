#!/usr/bin/env python3
"""
Test script to verify the setup and basic functionality of the Stock Prediction Website.
"""

import os
import sys
import requests
import json
from datetime import datetime

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_backend_server():
    """Test if the backend server is running and responding."""
    print("🔄 Testing backend server...")
    
    try:
        response = requests.get('http://127.0.0.1:5000/api/health', timeout=5)
        if response.status_code == 200:
            print("✅ Backend server is running and responding")
            return True
        else:
            print(f"❌ Backend server responded with status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ Backend server is not running or not accessible")
        print("   Please start the server with: python backend/run.py")
        return False
    except Exception as e:
        print(f"❌ Error testing backend server: {e}")
        return False

def test_user_registration():
    """Test user registration functionality."""
    print("\n🔄 Testing user registration...")
    
    test_user = {
        "username": f"testuser_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
        "email": f"test_{datetime.now().strftime('%Y%m%d_%H%M%S')}@example.com",
        "password": "TestPassword123!",
        "first_name": "Test",
        "last_name": "User"
    }
    
    try:
        response = requests.post(
            'http://127.0.0.1:5000/api/auth/register',
            json=test_user,
            timeout=10
        )
        
        if response.status_code == 201:
            data = response.json()
            print("✅ User registration successful")
            print(f"   User ID: {data.get('user', {}).get('id', 'N/A')}")
            return data.get('access_token'), test_user
        else:
            print(f"❌ User registration failed with status {response.status_code}")
            print(f"   Response: {response.text}")
            return None, None
            
    except Exception as e:
        print(f"❌ Error testing user registration: {e}")
        return None, None

def test_user_login(test_user):
    """Test user login functionality."""
    print("\n🔄 Testing user login...")
    
    login_data = {
        "username": test_user["username"],
        "password": test_user["password"]
    }
    
    try:
        response = requests.post(
            'http://127.0.0.1:5000/api/auth/login',
            json=login_data,
            timeout=10
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ User login successful")
            return data.get('access_token')
        else:
            print(f"❌ User login failed with status {response.status_code}")
            print(f"   Response: {response.text}")
            return None
            
    except Exception as e:
        print(f"❌ Error testing user login: {e}")
        return None

def test_theme_preferences(token):
    """Test theme preference functionality."""
    print("\n🔄 Testing theme preferences...")
    
    headers = {'Authorization': f'Bearer {token}'}
    
    try:
        # Test getting preferences
        response = requests.get(
            'http://127.0.0.1:5000/api/user/preferences',
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            print("✅ Get preferences successful")
            
            # Test updating theme
            theme_data = {"theme": "dark"}
            response = requests.put(
                'http://127.0.0.1:5000/api/user/preferences/theme',
                json=theme_data,
                headers=headers,
                timeout=10
            )
            
            if response.status_code == 200:
                print("✅ Theme update successful")
                return True
            else:
                print(f"❌ Theme update failed with status {response.status_code}")
                return False
        else:
            print(f"❌ Get preferences failed with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing theme preferences: {e}")
        return False

def test_stock_prediction(token):
    """Test stock prediction functionality."""
    print("\n🔄 Testing stock prediction...")
    
    headers = {'Authorization': f'Bearer {token}'}
    prediction_data = {
        "symbol": "AAPL",
        "prediction_date": "2024-12-31"
    }
    
    try:
        response = requests.post(
            'http://127.0.0.1:5000/api/predict/stock',
            json=prediction_data,
            headers=headers,
            timeout=15
        )
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Stock prediction successful")
            print(f"   Symbol: {data.get('details', {}).get('symbol', 'N/A')}")
            print(f"   Direction: {data.get('details', {}).get('predicted_direction', 'N/A')}")
            print(f"   Confidence: {data.get('details', {}).get('confidence_score', 'N/A')}")
            return True
        else:
            print(f"❌ Stock prediction failed with status {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing stock prediction: {e}")
        return False

def test_frontend_files():
    """Test if frontend files exist and are accessible."""
    print("\n🔄 Testing frontend files...")
    
    required_files = [
        'frontend/index.html',
        'frontend/dashboard.html',
        'frontend/css/styles.css',
        'frontend/css/auth.css',
        'frontend/css/dashboard.css',
        'frontend/js/auth.js',
        'frontend/js/theme.js',
        'frontend/js/dashboard.js'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ Missing frontend files:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    else:
        print("✅ All frontend files are present")
        return True

def main():
    """Run all tests."""
    print("🚀 Starting Stock Prediction Website Tests")
    print("=" * 50)
    
    # Test frontend files
    frontend_ok = test_frontend_files()
    
    # Test backend server
    server_ok = test_backend_server()
    
    if not server_ok:
        print("\n❌ Cannot proceed with API tests - server is not running")
        print("\n💡 To start the server:")
        print("   1. Install dependencies: pip install -r requirements.txt")
        print("   2. Start server: python backend/run.py")
        return
    
    # Test user registration
    token, test_user = test_user_registration()
    
    if not token:
        print("\n❌ Cannot proceed with authenticated tests - registration failed")
        return
    
    # Test user login
    login_token = test_user_login(test_user)
    
    if not login_token:
        print("\n❌ Login test failed")
        return
    
    # Test theme preferences
    theme_ok = test_theme_preferences(token)
    
    # Test stock prediction
    prediction_ok = test_stock_prediction(token)
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    print(f"   Frontend Files: {'✅' if frontend_ok else '❌'}")
    print(f"   Backend Server: {'✅' if server_ok else '❌'}")
    print(f"   User Registration: {'✅' if token else '❌'}")
    print(f"   User Login: {'✅' if login_token else '❌'}")
    print(f"   Theme Preferences: {'✅' if theme_ok else '❌'}")
    print(f"   Stock Prediction: {'✅' if prediction_ok else '❌'}")
    
    all_passed = all([frontend_ok, server_ok, token, login_token, theme_ok, prediction_ok])
    
    if all_passed:
        print("\n🎉 All tests passed! Your Stock Prediction Website is working correctly.")
        print("\n🌐 You can now access the website at:")
        print("   - Login page: http://127.0.0.1:5000 (serve frontend/index.html)")
        print("   - API endpoints: http://127.0.0.1:5000/api/")
    else:
        print("\n⚠️  Some tests failed. Please check the errors above and fix them.")

if __name__ == '__main__':
    main()
