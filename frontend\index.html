<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Stock Prediction - Login</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/auth.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="auth-container">
        <div class="auth-card">
            <div class="auth-header">
                <div class="logo">
                    <i class="fas fa-chart-line"></i>
                    <h1>StockPredict</h1>
                </div>
                <p class="tagline">AI-Powered Stock Market Predictions</p>
            </div>

            <!-- Login Form -->
            <form id="loginForm" class="auth-form active">
                <h2>Welcome Back</h2>
                <p class="form-subtitle">Sign in to your account</p>
                
                <div class="form-group">
                    <label for="loginUsername">Username or Email</label>
                    <div class="input-group">
                        <i class="fas fa-user"></i>
                        <input type="text" id="loginUsername" name="username" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="loginPassword">Password</label>
                    <div class="input-group">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="loginPassword" name="password" required>
                        <button type="button" class="password-toggle" onclick="togglePassword('loginPassword')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="form-options">
                    <label class="checkbox-container">
                        <input type="checkbox" id="rememberMe">
                        <span class="checkmark"></span>
                        Remember me
                    </label>
                    <a href="#" class="forgot-password">Forgot password?</a>
                </div>

                <button type="submit" class="btn btn-primary">
                    <span class="btn-text">Sign In</span>
                    <span class="btn-loader" style="display: none;">
                        <i class="fas fa-spinner fa-spin"></i>
                    </span>
                </button>

                <div class="form-footer">
                    <p>Don't have an account? 
                        <a href="#" onclick="switchForm('register')">Sign up</a>
                    </p>
                </div>
            </form>

            <!-- Register Form -->
            <form id="registerForm" class="auth-form">
                <h2>Create Account</h2>
                <p class="form-subtitle">Join us to start predicting stocks</p>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="firstName">First Name</label>
                        <div class="input-group">
                            <i class="fas fa-user"></i>
                            <input type="text" id="firstName" name="first_name">
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="lastName">Last Name</label>
                        <div class="input-group">
                            <i class="fas fa-user"></i>
                            <input type="text" id="lastName" name="last_name">
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="registerUsername">Username</label>
                    <div class="input-group">
                        <i class="fas fa-user-circle"></i>
                        <input type="text" id="registerUsername" name="username" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="registerEmail">Email</label>
                    <div class="input-group">
                        <i class="fas fa-envelope"></i>
                        <input type="email" id="registerEmail" name="email" required>
                    </div>
                </div>

                <div class="form-group">
                    <label for="registerPassword">Password</label>
                    <div class="input-group">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="registerPassword" name="password" required>
                        <button type="button" class="password-toggle" onclick="togglePassword('registerPassword')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                    <div class="password-requirements">
                        <small>Password must contain at least 8 characters, including uppercase, lowercase, and numbers</small>
                    </div>
                </div>

                <div class="form-group">
                    <label for="confirmPassword">Confirm Password</label>
                    <div class="input-group">
                        <i class="fas fa-lock"></i>
                        <input type="password" id="confirmPassword" name="confirm_password" required>
                        <button type="button" class="password-toggle" onclick="togglePassword('confirmPassword')">
                            <i class="fas fa-eye"></i>
                        </button>
                    </div>
                </div>

                <div class="form-options">
                    <label class="checkbox-container">
                        <input type="checkbox" id="agreeTerms" required>
                        <span class="checkmark"></span>
                        I agree to the <a href="#" target="_blank">Terms of Service</a> and <a href="#" target="_blank">Privacy Policy</a>
                    </label>
                </div>

                <button type="submit" class="btn btn-primary">
                    <span class="btn-text">Create Account</span>
                    <span class="btn-loader" style="display: none;">
                        <i class="fas fa-spinner fa-spin"></i>
                    </span>
                </button>

                <div class="form-footer">
                    <p>Already have an account? 
                        <a href="#" onclick="switchForm('login')">Sign in</a>
                    </p>
                </div>
            </form>
        </div>

        <!-- Theme Toggle -->
        <div class="theme-toggle">
            <button id="themeToggle" class="theme-btn" title="Toggle theme">
                <i class="fas fa-moon"></i>
            </button>
        </div>
    </div>

    <!-- Alert/Notification -->
    <div id="notification" class="notification"></div>

    <script src="js/auth.js"></script>
    <script src="js/theme.js"></script>
</body>
</html>
