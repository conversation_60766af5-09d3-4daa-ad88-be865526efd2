/* Dashboard Styles */

/* Navigation */
.navbar {
    background-color: var(--bg-card);
    border-bottom: 1px solid var(--border-color);
    box-shadow: var(--shadow-sm);
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 4rem;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
}

.nav-brand i {
    font-size: 1.5rem;
}

.nav-menu {
    display: flex;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    font-weight: 500;
}

.nav-link:hover,
.nav-link.active {
    color: var(--primary-color);
    background-color: var(--bg-secondary);
}

.nav-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

/* User Menu */
.user-menu {
    position: relative;
}

.user-btn {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 1rem;
    background: none;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    color: var(--text-primary);
}

.user-btn:hover {
    border-color: var(--border-focus);
    background-color: var(--bg-secondary);
}

.user-avatar {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color), var(--primary-hover));
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.875rem;
}

.user-name {
    font-weight: 500;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 0.5rem;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-lg);
    min-width: 200px;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
    z-index: 1000;
}

.user-dropdown.show {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.75rem 1rem;
    color: var(--text-primary);
    text-decoration: none;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    cursor: pointer;
    transition: background-color var(--transition-fast);
}

.dropdown-item:hover {
    background-color: var(--bg-secondary);
}

.dropdown-divider {
    height: 1px;
    background-color: var(--border-color);
    margin: 0.5rem 0;
}

/* Main Content */
.main-content {
    min-height: calc(100vh - 4rem);
    background-color: var(--bg-primary);
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
    animation: fadeIn 0.3s ease-out;
}

.page-header {
    margin-bottom: 2rem;
}

.page-header h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.page-header p {
    color: var(--text-secondary);
    font-size: 1rem;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 3rem;
}

.stat-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 1.5rem;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all var(--transition-fast);
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: var(--radius-md);
    background: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.25rem;
}

.stat-icon.success {
    background: var(--accent-color);
}

.stat-icon.warning {
    background: var(--warning-color);
}

.stat-icon.info {
    background: var(--primary-color);
}

.stat-content h3 {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
    color: var(--text-primary);
}

.stat-content p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.875rem;
}

/* Quick Actions */
.quick-actions {
    margin-bottom: 3rem;
}

.quick-actions h2 {
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
}

.action-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 2rem;
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    color: inherit;
}

.action-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-color);
}

.action-card i {
    font-size: 2.5rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.action-card h3 {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.action-card p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 0.875rem;
}

/* Recent Predictions */
.recent-predictions {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 2rem;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.section-header h2 {
    margin: 0;
    color: var(--text-primary);
}

.predictions-list {
    min-height: 200px;
}

.loading-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 3rem;
    color: var(--text-secondary);
}

.loading-state i {
    font-size: 2rem;
    margin-bottom: 1rem;
}

/* Prediction Form */
.predict-form-container {
    max-width: 600px;
    margin: 0 auto;
}

.predict-form {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 2rem;
    margin-bottom: 2rem;
}

.search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 var(--radius-md) var(--radius-md);
    max-height: 200px;
    overflow-y: auto;
    z-index: 10;
    display: none;
}

.search-result-item {
    padding: 0.75rem 1rem;
    cursor: pointer;
    border-bottom: 1px solid var(--border-color);
    transition: background-color var(--transition-fast);
}

.search-result-item:hover {
    background-color: var(--bg-secondary);
}

.search-result-item:last-child {
    border-bottom: none;
}

/* Prediction Result */
.prediction-result {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 2rem;
    animation: fadeIn 0.5s ease-out;
}

.result-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.result-symbol {
    font-size: 1.25rem;
    font-weight: 700;
    color: var(--primary-color);
}

.prediction-direction {
    text-align: center;
    margin-bottom: 2rem;
}

.direction-icon {
    width: 4rem;
    height: 4rem;
    border-radius: 50%;
    margin: 0 auto 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: white;
}

.direction-icon.up {
    background: var(--accent-color);
}

.direction-icon.down {
    background: var(--danger-color);
}

.direction-icon.neutral {
    background: var(--secondary-color);
}

.direction-text {
    font-size: 1.5rem;
    font-weight: 600;
    text-transform: capitalize;
}

.confidence-score {
    margin-bottom: 2rem;
}

.confidence-score label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--text-primary);
}

.confidence-bar {
    position: relative;
    height: 1rem;
    background: var(--bg-secondary);
    border-radius: var(--radius-md);
    overflow: hidden;
}

.confidence-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--warning-color), var(--accent-color));
    border-radius: var(--radius-md);
    transition: width 0.5s ease-out;
}

.confidence-text {
    position: absolute;
    top: 50%;
    right: 0.5rem;
    transform: translateY(-50%);
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--text-primary);
}

.prediction-reasoning h4 {
    margin-bottom: 0.5rem;
    color: var(--text-primary);
}

.prediction-reasoning p {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Settings Styles */
.settings-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
}

.settings-card {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: 2rem;
}

.settings-card h3 {
    margin-bottom: 1.5rem;
    color: var(--text-primary);
}

.preference-group {
    margin-bottom: 1.5rem;
}

.preference-group label {
    display: block;
    margin-bottom: 1rem;
    font-weight: 600;
    color: var(--text-primary);
}

.theme-options {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.radio-option {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: var(--radius-md);
    transition: background-color var(--transition-fast);
}

.radio-option:hover {
    background-color: var(--bg-secondary);
}

.radio-option input[type="radio"] {
    display: none;
}

.radio-custom {
    width: 1.25rem;
    height: 1.25rem;
    border: 2px solid var(--border-color);
    border-radius: 50%;
    position: relative;
    transition: all var(--transition-fast);
}

.radio-option input[type="radio"]:checked + .radio-custom {
    border-color: var(--primary-color);
}

.radio-option input[type="radio"]:checked + .radio-custom::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 0.5rem;
    height: 0.5rem;
    background-color: var(--primary-color);
    border-radius: 50%;
}

/* History Styles */
.history-filters {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
}

.filter-group {
    flex: 1;
}

.filter-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
}

.filter-group input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--bg-primary);
    color: var(--text-primary);
}

.history-list {
    background: var(--bg-card);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    min-height: 400px;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 1rem;
    margin-top: 2rem;
}

.page-info {
    color: var(--text-secondary);
    font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-container {
        padding: 0 0.5rem;
    }

    .nav-menu {
        display: none;
    }

    .container {
        padding: 1rem 0.5rem;
    }

    .stats-grid {
        grid-template-columns: 1fr;
    }

    .action-grid {
        grid-template-columns: 1fr;
    }

    .section-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .predict-form-container {
        max-width: 100%;
    }

    .result-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }

    .settings-grid {
        grid-template-columns: 1fr;
    }

    .history-filters {
        flex-direction: column;
    }
}
