"""
Stock Prediction ML Model
This module contains the machine learning model for predicting stock price movements.
"""

import pandas as pd
import numpy as np
import yfinance as yf
from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
import joblib
import os
import warnings
warnings.filterwarnings('ignore')

class StockPredictor:
    """
    A machine learning model for predicting stock price movements.
    This is a basic implementation that can be enhanced with more sophisticated features.
    """
    
    def __init__(self, model_path=None):
        self.model = None
        self.scaler = StandardScaler()
        self.feature_columns = []
        self.model_path = model_path or 'backend/ml_models/saved_models'
        self.is_trained = False
        
        # Create model directory if it doesn't exist
        os.makedirs(self.model_path, exist_ok=True)
    
    def fetch_stock_data(self, symbol, period='2y'):
        """
        Fetch historical stock data for the given symbol.
        
        Args:
            symbol (str): Stock symbol (e.g., 'AAPL')
            period (str): Time period for data ('1y', '2y', '5y', etc.)
        
        Returns:
            pd.DataFrame: Historical stock data
        """
        try:
            stock = yf.Ticker(symbol)
            data = stock.history(period=period)
            
            if data.empty:
                raise ValueError(f"No data found for symbol {symbol}")
            
            return data
        except Exception as e:
            print(f"Error fetching data for {symbol}: {e}")
            return None
    
    def create_features(self, data):
        """
        Create technical indicators and features for the ML model.
        
        Args:
            data (pd.DataFrame): Raw stock data
        
        Returns:
            pd.DataFrame: Data with engineered features
        """
        df = data.copy()
        
        # Basic price features
        df['price_change'] = df['Close'].pct_change()
        df['high_low_ratio'] = df['High'] / df['Low']
        df['volume_change'] = df['Volume'].pct_change()
        
        # Moving averages
        df['ma_5'] = df['Close'].rolling(window=5).mean()
        df['ma_10'] = df['Close'].rolling(window=10).mean()
        df['ma_20'] = df['Close'].rolling(window=20).mean()
        df['ma_50'] = df['Close'].rolling(window=50).mean()
        
        # Moving average ratios
        df['ma_5_20_ratio'] = df['ma_5'] / df['ma_20']
        df['ma_10_50_ratio'] = df['ma_10'] / df['ma_50']
        df['price_ma_20_ratio'] = df['Close'] / df['ma_20']
        
        # RSI (Relative Strength Index)
        df['rsi'] = self.calculate_rsi(df['Close'])
        
        # MACD
        df['macd'], df['macd_signal'] = self.calculate_macd(df['Close'])
        df['macd_histogram'] = df['macd'] - df['macd_signal']
        
        # Bollinger Bands
        df['bb_upper'], df['bb_lower'] = self.calculate_bollinger_bands(df['Close'])
        df['bb_position'] = (df['Close'] - df['bb_lower']) / (df['bb_upper'] - df['bb_lower'])
        
        # Volume indicators
        df['volume_ma_10'] = df['Volume'].rolling(window=10).mean()
        df['volume_ratio'] = df['Volume'] / df['volume_ma_10']
        
        # Volatility
        df['volatility'] = df['price_change'].rolling(window=20).std()
        
        # Target variable (next day's direction)
        df['next_day_return'] = df['Close'].shift(-1) / df['Close'] - 1
        df['target'] = (df['next_day_return'] > 0).astype(int)  # 1 for up, 0 for down
        
        return df
    
    def calculate_rsi(self, prices, window=14):
        """Calculate Relative Strength Index."""
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=window).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=window).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi
    
    def calculate_macd(self, prices, fast=12, slow=26, signal=9):
        """Calculate MACD indicator."""
        ema_fast = prices.ewm(span=fast).mean()
        ema_slow = prices.ewm(span=slow).mean()
        macd = ema_fast - ema_slow
        macd_signal = macd.ewm(span=signal).mean()
        return macd, macd_signal
    
    def calculate_bollinger_bands(self, prices, window=20, num_std=2):
        """Calculate Bollinger Bands."""
        ma = prices.rolling(window=window).mean()
        std = prices.rolling(window=window).std()
        upper_band = ma + (std * num_std)
        lower_band = ma - (std * num_std)
        return upper_band, lower_band
    
    def prepare_training_data(self, df):
        """
        Prepare data for training by selecting features and handling missing values.
        
        Args:
            df (pd.DataFrame): DataFrame with features
        
        Returns:
            tuple: (X, y) training data
        """
        # Select feature columns (exclude price columns and target)
        feature_cols = [
            'price_change', 'high_low_ratio', 'volume_change',
            'ma_5_20_ratio', 'ma_10_50_ratio', 'price_ma_20_ratio',
            'rsi', 'macd', 'macd_signal', 'macd_histogram',
            'bb_position', 'volume_ratio', 'volatility'
        ]
        
        # Filter to only include columns that exist
        available_cols = [col for col in feature_cols if col in df.columns]
        self.feature_columns = available_cols
        
        # Prepare features and target
        X = df[available_cols].copy()
        y = df['target'].copy()
        
        # Remove rows with missing values
        mask = ~(X.isnull().any(axis=1) | y.isnull())
        X = X[mask]
        y = y[mask]
        
        return X, y
    
    def train_model(self, symbols=['AAPL', 'GOOGL', 'MSFT', 'TSLA', 'AMZN'], test_size=0.2):
        """
        Train the stock prediction model using data from multiple symbols.
        
        Args:
            symbols (list): List of stock symbols to train on
            test_size (float): Proportion of data to use for testing
        
        Returns:
            dict: Training results including accuracy
        """
        print("🔄 Training stock prediction model...")
        
        all_X = []
        all_y = []
        
        # Collect data from all symbols
        for symbol in symbols:
            print(f"   Fetching data for {symbol}...")
            data = self.fetch_stock_data(symbol)
            
            if data is not None:
                df_features = self.create_features(data)
                X, y = self.prepare_training_data(df_features)
                
                if len(X) > 0:
                    all_X.append(X)
                    all_y.append(y)
        
        if not all_X:
            raise ValueError("No valid training data found")
        
        # Combine all data
        X_combined = pd.concat(all_X, ignore_index=True)
        y_combined = pd.concat(all_y, ignore_index=True)
        
        print(f"   Total training samples: {len(X_combined)}")
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X_combined, y_combined, test_size=test_size, random_state=42, stratify=y_combined
        )
        
        # Scale features
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)
        
        # Train model
        self.model = RandomForestClassifier(
            n_estimators=100,
            max_depth=10,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=42
        )
        
        self.model.fit(X_train_scaled, y_train)
        
        # Evaluate model
        train_accuracy = self.model.score(X_train_scaled, y_train)
        test_accuracy = self.model.score(X_test_scaled, y_test)
        
        self.is_trained = True
        
        results = {
            'train_accuracy': train_accuracy,
            'test_accuracy': test_accuracy,
            'training_samples': len(X_train),
            'test_samples': len(X_test),
            'features_used': self.feature_columns
        }
        
        print(f"✅ Model training completed!")
        print(f"   Training accuracy: {train_accuracy:.3f}")
        print(f"   Test accuracy: {test_accuracy:.3f}")
        
        return results
    
    def predict_stock_movement(self, symbol, prediction_date=None):
        """
        Predict stock movement for a given symbol.
        
        Args:
            symbol (str): Stock symbol
            prediction_date (str): Date for prediction (YYYY-MM-DD)
        
        Returns:
            dict: Prediction results
        """
        if not self.is_trained:
            # Try to load a pre-trained model
            if not self.load_model():
                # Train a simple model if none exists
                print("No trained model found. Training a basic model...")
                self.train_model()
        
        try:
            # Fetch recent data
            data = self.fetch_stock_data(symbol, period='6mo')
            
            if data is None or len(data) < 50:
                raise ValueError(f"Insufficient data for {symbol}")
            
            # Create features
            df_features = self.create_features(data)
            
            # Get the most recent complete row (excluding the last row which might have NaN target)
            latest_data = df_features.iloc[-2:-1][self.feature_columns]
            
            if latest_data.isnull().any().any():
                raise ValueError("Missing values in latest data")
            
            # Scale features
            latest_scaled = self.scaler.transform(latest_data)
            
            # Make prediction
            prediction = self.model.predict(latest_scaled)[0]
            prediction_proba = self.model.predict_proba(latest_scaled)[0]
            
            # Get confidence (probability of predicted class)
            confidence = prediction_proba[prediction]
            
            # Convert to direction
            direction = 'up' if prediction == 1 else 'down'
            
            # Get feature importance for explanation
            feature_importance = dict(zip(
                self.feature_columns,
                self.model.feature_importances_
            ))
            
            # Sort by importance
            top_features = sorted(feature_importance.items(), key=lambda x: x[1], reverse=True)[:3]
            
            return {
                'symbol': symbol.upper(),
                'predicted_direction': direction,
                'confidence_score': float(confidence),
                'prediction_date': prediction_date or datetime.now().strftime('%Y-%m-%d'),
                'model_version': 'v1.0',
                'top_features': top_features,
                'latest_price': float(data['Close'].iloc[-1]),
                'success': True
            }
            
        except Exception as e:
            print(f"Error predicting for {symbol}: {e}")
            return {
                'symbol': symbol.upper(),
                'error': str(e),
                'success': False
            }
    
    def save_model(self):
        """Save the trained model and scaler."""
        if not self.is_trained:
            print("No trained model to save")
            return False
        
        try:
            model_file = os.path.join(self.model_path, 'stock_predictor.joblib')
            scaler_file = os.path.join(self.model_path, 'scaler.joblib')
            features_file = os.path.join(self.model_path, 'features.joblib')
            
            joblib.dump(self.model, model_file)
            joblib.dump(self.scaler, scaler_file)
            joblib.dump(self.feature_columns, features_file)
            
            print(f"✅ Model saved to {self.model_path}")
            return True
            
        except Exception as e:
            print(f"Error saving model: {e}")
            return False
    
    def load_model(self):
        """Load a pre-trained model and scaler."""
        try:
            model_file = os.path.join(self.model_path, 'stock_predictor.joblib')
            scaler_file = os.path.join(self.model_path, 'scaler.joblib')
            features_file = os.path.join(self.model_path, 'features.joblib')
            
            if all(os.path.exists(f) for f in [model_file, scaler_file, features_file]):
                self.model = joblib.load(model_file)
                self.scaler = joblib.load(scaler_file)
                self.feature_columns = joblib.load(features_file)
                self.is_trained = True
                
                print(f"✅ Model loaded from {self.model_path}")
                return True
            else:
                print("No saved model found")
                return False
                
        except Exception as e:
            print(f"Error loading model: {e}")
            return False

# Global predictor instance
_predictor_instance = None

def get_predictor():
    """Get or create a global predictor instance."""
    global _predictor_instance
    if _predictor_instance is None:
        _predictor_instance = StockPredictor()
    return _predictor_instance
