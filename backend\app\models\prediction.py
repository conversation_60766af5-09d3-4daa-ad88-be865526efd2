from datetime import datetime
from backend.app import db
import uuid

class StockPrediction(db.Model):
    """Model for storing stock prediction results."""
    
    __tablename__ = 'stock_predictions'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    user_id = db.Column(db.String(36), db.<PERSON>ey('users.id'), nullable=False)
    stock_symbol = db.Column(db.String(10), nullable=False, index=True)
    prediction_date = db.Column(db.Date, nullable=False)
    predicted_direction = db.Column(db.String(10), nullable=False)  # 'up', 'down', 'neutral'
    confidence_score = db.Column(db.Float, nullable=True)
    actual_direction = db.Column(db.String(10), nullable=True)  # To be updated later
    model_version = db.Column(db.String(20), nullable=False)
    features_used = db.Column(db.Text, nullable=True)  # JSON string of features
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    def __init__(self, user_id, stock_symbol, prediction_date, predicted_direction, 
                 confidence_score=None, model_version='v1.0', features_used=None):
        self.user_id = user_id
        self.stock_symbol = stock_symbol.upper()
        self.prediction_date = prediction_date
        self.predicted_direction = predicted_direction
        self.confidence_score = confidence_score
        self.model_version = model_version
        self.features_used = features_used
    
    def to_dict(self):
        """Convert prediction object to dictionary for JSON serialization."""
        return {
            'id': self.id,
            'user_id': self.user_id,
            'stock_symbol': self.stock_symbol,
            'prediction_date': self.prediction_date.isoformat() if self.prediction_date else None,
            'predicted_direction': self.predicted_direction,
            'confidence_score': self.confidence_score,
            'actual_direction': self.actual_direction,
            'model_version': self.model_version,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f'<StockPrediction {self.stock_symbol} - {self.predicted_direction}>'

class StockData(db.Model):
    """Model for storing historical stock data."""
    
    __tablename__ = 'stock_data'
    
    id = db.Column(db.String(36), primary_key=True, default=lambda: str(uuid.uuid4()))
    symbol = db.Column(db.String(10), nullable=False, index=True)
    date = db.Column(db.Date, nullable=False, index=True)
    open_price = db.Column(db.Float, nullable=False)
    high_price = db.Column(db.Float, nullable=False)
    low_price = db.Column(db.Float, nullable=False)
    close_price = db.Column(db.Float, nullable=False)
    volume = db.Column(db.BigInteger, nullable=False)
    adjusted_close = db.Column(db.Float, nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, nullable=False)
    
    # Composite unique constraint
    __table_args__ = (db.UniqueConstraint('symbol', 'date', name='unique_symbol_date'),)
    
    def __init__(self, symbol, date, open_price, high_price, low_price, 
                 close_price, volume, adjusted_close=None):
        self.symbol = symbol.upper()
        self.date = date
        self.open_price = open_price
        self.high_price = high_price
        self.low_price = low_price
        self.close_price = close_price
        self.volume = volume
        self.adjusted_close = adjusted_close
    
    def to_dict(self):
        """Convert stock data object to dictionary for JSON serialization."""
        return {
            'id': self.id,
            'symbol': self.symbol,
            'date': self.date.isoformat() if self.date else None,
            'open_price': self.open_price,
            'high_price': self.high_price,
            'low_price': self.low_price,
            'close_price': self.close_price,
            'volume': self.volume,
            'adjusted_close': self.adjusted_close,
            'created_at': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f'<StockData {self.symbol} - {self.date}>'
