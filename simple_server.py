#!/usr/bin/env python3
"""
Simple server to test the stock prediction website.
"""

import os
import sys

# Add current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("🔄 Starting Stock Prediction Website...")
    print("📍 Current directory:", os.getcwd())
    
    # Test imports
    print("🔍 Testing imports...")
    from backend.app import create_app, db
    print("✅ Backend imports successful")
    
    # Create Flask app
    print("🔧 Creating Flask application...")
    app = create_app('development')
    
    # Initialize database
    print("🗄️ Initializing database...")
    with app.app_context():
        db.create_all()
        print("✅ Database initialized")
    
    # Start server
    print("🚀 Starting server...")
    print("🌐 Backend API will be available at: http://127.0.0.1:5000")
    print("📁 Frontend files are in the 'frontend' directory")
    print("💡 Open frontend/index.html in your browser or serve it with a web server")
    print("\n" + "="*50)
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True
    )
    
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("💡 Make sure all dependencies are installed:")
    print("   pip install flask flask-sqlalchemy flask-jwt-extended flask-cors python-dotenv python-decouple")
    
except Exception as e:
    print(f"❌ Error starting server: {e}")
    import traceback
    traceback.print_exc()
