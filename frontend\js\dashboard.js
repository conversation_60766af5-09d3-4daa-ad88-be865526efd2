// Dashboard JavaScript

// API Configuration
const API_BASE_URL = 'http://127.0.0.1:5000/api';

// Global state
let currentUser = null;
let currentPage = 1;
let totalPages = 1;

// Initialize dashboard
document.addEventListener('DOMContentLoaded', function() {
    // Check authentication
    checkAuthentication();
    
    // Initialize components
    initializeNavigation();
    initializeUserMenu();
    initializePredictionForm();
    initializeSettings();
    
    // Load initial data
    loadDashboardData();
    
    // Set default prediction date to tomorrow
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    document.getElementById('predictionDate').value = tomorrow.toISOString().split('T')[0];
});

// Check if user is authenticated
function checkAuthentication() {
    const token = localStorage.getItem('access_token');
    const userData = localStorage.getItem('user_data');
    
    if (!token || !userData) {
        window.location.href = 'index.html';
        return;
    }
    
    try {
        currentUser = JSON.parse(userData);
        updateUserDisplay();
    } catch (error) {
        console.error('Error parsing user data:', error);
        logout();
    }
}

// Update user display in navigation
function updateUserDisplay() {
    const userName = document.getElementById('userName');
    if (userName && currentUser) {
        const displayName = currentUser.first_name || currentUser.username;
        userName.textContent = displayName;
    }
}

// Initialize navigation
function initializeNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            const target = this.getAttribute('href').substring(1);
            showSection(target);
            
            // Update active nav link
            navLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
        });
    });
}

// Show specific section
function showSection(sectionName) {
    const sections = document.querySelectorAll('.content-section');
    sections.forEach(section => section.classList.remove('active'));
    
    const targetSection = document.getElementById(`${sectionName}-section`);
    if (targetSection) {
        targetSection.classList.add('active');
        
        // Load section-specific data
        switch(sectionName) {
            case 'history':
                loadPredictionHistory();
                break;
            case 'settings':
                loadUserSettings();
                break;
        }
    }
}

// Initialize user menu
function initializeUserMenu() {
    const userMenuBtn = document.getElementById('userMenuBtn');
    const userDropdown = document.getElementById('userDropdown');
    const logoutBtn = document.getElementById('logoutBtn');
    
    if (userMenuBtn && userDropdown) {
        userMenuBtn.addEventListener('click', function() {
            userDropdown.classList.toggle('show');
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!userMenuBtn.contains(e.target)) {
                userDropdown.classList.remove('show');
            }
        });
    }
    
    if (logoutBtn) {
        logoutBtn.addEventListener('click', logout);
    }
}

// Logout function
async function logout() {
    try {
        const token = localStorage.getItem('access_token');
        
        if (token) {
            await fetch(`${API_BASE_URL}/auth/logout`, {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });
        }
    } catch (error) {
        console.error('Logout error:', error);
    } finally {
        // Clear local storage and redirect
        localStorage.removeItem('access_token');
        localStorage.removeItem('user_data');
        window.location.href = 'index.html';
    }
}

// Load dashboard data
async function loadDashboardData() {
    try {
        const token = localStorage.getItem('access_token');
        
        const response = await fetch(`${API_BASE_URL}/user/dashboard`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            updateDashboardStats(data);
            updateRecentPredictions(data.recent_predictions || []);
        } else {
            console.error('Failed to load dashboard data');
        }
    } catch (error) {
        console.error('Error loading dashboard data:', error);
    }
}

// Update dashboard statistics
function updateDashboardStats(data) {
    const stats = data.stats || {};
    
    document.getElementById('totalPredictions').textContent = stats.total_predictions || 0;
    document.getElementById('accurateCount').textContent = '0'; // Placeholder
    document.getElementById('accuracyRate').textContent = '0%'; // Placeholder
    document.getElementById('memberSince').textContent = stats.member_since || '-';
}

// Update recent predictions display
function updateRecentPredictions(predictions) {
    const container = document.getElementById('recentPredictionsList');
    
    if (predictions.length === 0) {
        container.innerHTML = `
            <div class="loading-state">
                <i class="fas fa-chart-line"></i>
                <p>No predictions yet. <a href="#" onclick="showSection('predict')">Make your first prediction!</a></p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = predictions.map(prediction => `
        <div class="prediction-item">
            <div class="prediction-symbol">${prediction.stock_symbol}</div>
            <div class="prediction-direction ${prediction.predicted_direction}">
                <i class="fas fa-arrow-${prediction.predicted_direction === 'up' ? 'up' : 'down'}"></i>
                ${prediction.predicted_direction.toUpperCase()}
            </div>
            <div class="prediction-confidence">${Math.round(prediction.confidence_score * 100)}%</div>
            <div class="prediction-date">${new Date(prediction.created_at).toLocaleDateString()}</div>
        </div>
    `).join('');
}

// Initialize prediction form
function initializePredictionForm() {
    const predictForm = document.getElementById('predictForm');
    const stockSymbolInput = document.getElementById('stockSymbol');
    const searchBtn = document.getElementById('searchBtn');
    
    if (predictForm) {
        predictForm.addEventListener('submit', handlePredictionSubmit);
    }
    
    if (stockSymbolInput) {
        stockSymbolInput.addEventListener('input', handleStockSearch);
        stockSymbolInput.addEventListener('focus', showSearchResults);
        stockSymbolInput.addEventListener('blur', hideSearchResults);
    }
    
    if (searchBtn) {
        searchBtn.addEventListener('click', function() {
            stockSymbolInput.focus();
        });
    }
}

// Handle stock search
async function handleStockSearch(event) {
    const query = event.target.value.trim();
    
    if (query.length < 1) {
        hideSearchResults();
        return;
    }
    
    try {
        const token = localStorage.getItem('access_token');
        const response = await fetch(`${API_BASE_URL}/predict/stocks/search?q=${encodeURIComponent(query)}`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            displaySearchResults(data.stocks || []);
        }
    } catch (error) {
        console.error('Error searching stocks:', error);
    }
}

// Display search results
function displaySearchResults(stocks) {
    const resultsContainer = document.getElementById('searchResults');
    
    if (stocks.length === 0) {
        hideSearchResults();
        return;
    }
    
    resultsContainer.innerHTML = stocks.map(stock => `
        <div class="search-result-item" onclick="selectStock('${stock.symbol}', '${stock.name}')">
            <strong>${stock.symbol}</strong> - ${stock.name}
            <small>${stock.exchange}</small>
        </div>
    `).join('');
    
    resultsContainer.style.display = 'block';
}

// Select stock from search results
function selectStock(symbol, name) {
    document.getElementById('stockSymbol').value = symbol;
    hideSearchResults();
}

// Show/hide search results
function showSearchResults() {
    const resultsContainer = document.getElementById('searchResults');
    if (resultsContainer.innerHTML.trim()) {
        resultsContainer.style.display = 'block';
    }
}

function hideSearchResults() {
    setTimeout(() => {
        document.getElementById('searchResults').style.display = 'none';
    }, 200);
}

// Handle prediction form submission
async function handlePredictionSubmit(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const submitButton = form.querySelector('button[type="submit"]');
    
    // Show loading state
    setButtonLoading(submitButton, true);
    
    try {
        const token = localStorage.getItem('access_token');
        
        const response = await fetch(`${API_BASE_URL}/predict/stock`, {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                symbol: formData.get('symbol').toUpperCase(),
                prediction_date: formData.get('prediction_date')
            })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            displayPredictionResult(data);
            showNotification('Prediction generated successfully!', 'success');
        } else {
            showNotification(data.error || 'Failed to generate prediction', 'error');
        }
        
    } catch (error) {
        console.error('Prediction error:', error);
        showNotification('Network error. Please try again.', 'error');
    } finally {
        setButtonLoading(submitButton, false);
    }
}

// Display prediction result
function displayPredictionResult(data) {
    const resultContainer = document.getElementById('predictionResult');
    const details = data.details || {};
    
    // Update result display
    document.getElementById('resultSymbol').textContent = details.symbol;
    
    const directionElement = document.getElementById('predictionDirection');
    const directionIcon = directionElement.querySelector('.direction-icon');
    const directionText = directionElement.querySelector('.direction-text');
    
    directionIcon.className = `direction-icon ${details.predicted_direction}`;
    directionIcon.innerHTML = `<i class="fas fa-arrow-${details.predicted_direction === 'up' ? 'up' : details.predicted_direction === 'down' ? 'down' : 'right'}"></i>`;
    directionText.textContent = details.predicted_direction;
    
    // Update confidence score
    const confidencePercent = Math.round(details.confidence_score * 100);
    document.getElementById('confidenceFill').style.width = `${confidencePercent}%`;
    document.getElementById('confidenceText').textContent = `${confidencePercent}%`;
    
    // Update reasoning
    document.getElementById('predictionReasoning').querySelector('p').textContent = details.reasoning || 'Analysis based on technical indicators and market trends.';
    
    // Show result
    resultContainer.style.display = 'block';
    resultContainer.scrollIntoView({ behavior: 'smooth' });
}

// Load prediction history
async function loadPredictionHistory(page = 1) {
    try {
        const token = localStorage.getItem('access_token');
        const response = await fetch(`${API_BASE_URL}/predict/history?page=${page}&per_page=10`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            displayPredictionHistory(data.predictions || []);
            updatePagination(data.pagination || {});
        }
    } catch (error) {
        console.error('Error loading prediction history:', error);
    }
}

// Display prediction history
function displayPredictionHistory(predictions) {
    const container = document.getElementById('historyList');
    
    if (predictions.length === 0) {
        container.innerHTML = `
            <div class="loading-state">
                <i class="fas fa-history"></i>
                <p>No prediction history found.</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = `
        <div class="history-items">
            ${predictions.map(prediction => `
                <div class="history-item">
                    <div class="history-symbol">${prediction.stock_symbol}</div>
                    <div class="history-direction ${prediction.predicted_direction}">
                        <i class="fas fa-arrow-${prediction.predicted_direction === 'up' ? 'up' : 'down'}"></i>
                        ${prediction.predicted_direction.toUpperCase()}
                    </div>
                    <div class="history-confidence">${Math.round(prediction.confidence_score * 100)}%</div>
                    <div class="history-date">${new Date(prediction.created_at).toLocaleDateString()}</div>
                </div>
            `).join('')}
        </div>
    `;
}

// Update pagination
function updatePagination(pagination) {
    const paginationContainer = document.getElementById('historyPagination');
    const pageInfo = document.getElementById('pageInfo');
    const prevBtn = document.getElementById('prevPage');
    const nextBtn = document.getElementById('nextPage');
    
    if (pagination.pages > 1) {
        paginationContainer.style.display = 'flex';
        pageInfo.textContent = `Page ${pagination.page} of ${pagination.pages}`;
        
        prevBtn.disabled = !pagination.has_prev;
        nextBtn.disabled = !pagination.has_next;
        
        prevBtn.onclick = () => loadPredictionHistory(pagination.page - 1);
        nextBtn.onclick = () => loadPredictionHistory(pagination.page + 1);
    } else {
        paginationContainer.style.display = 'none';
    }
}

// Initialize settings
function initializeSettings() {
    const profileForm = document.getElementById('profileForm');
    const themeRadios = document.querySelectorAll('input[name="theme"]');
    const emailNotifications = document.getElementById('emailNotifications');
    
    if (profileForm) {
        profileForm.addEventListener('submit', handleProfileUpdate);
    }
    
    themeRadios.forEach(radio => {
        radio.addEventListener('change', handleThemeChange);
    });
    
    if (emailNotifications) {
        emailNotifications.addEventListener('change', handleNotificationChange);
    }
}

// Load user settings
async function loadUserSettings() {
    try {
        const token = localStorage.getItem('access_token');
        const response = await fetch(`${API_BASE_URL}/user/settings`, {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            populateSettingsForm(data);
        }
    } catch (error) {
        console.error('Error loading user settings:', error);
    }
}

// Populate settings form
function populateSettingsForm(data) {
    const profile = data.profile || {};
    const preferences = data.preferences || {};
    
    // Profile fields
    document.getElementById('profileFirstName').value = profile.first_name || '';
    document.getElementById('profileLastName').value = profile.last_name || '';
    document.getElementById('profileEmail').value = profile.email || '';
    
    // Theme preference
    const themeRadio = document.getElementById(`theme${preferences.theme === 'dark' ? 'Dark' : 'Light'}`);
    if (themeRadio) {
        themeRadio.checked = true;
    }
    
    // Email notifications
    const emailNotifications = document.getElementById('emailNotifications');
    if (emailNotifications) {
        emailNotifications.checked = preferences.email_notifications !== false;
    }
}

// Handle profile update
async function handleProfileUpdate(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    const submitButton = form.querySelector('button[type="submit"]');
    
    setButtonLoading(submitButton, true);
    
    try {
        const token = localStorage.getItem('access_token');
        
        const response = await fetch(`${API_BASE_URL}/auth/profile`, {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                first_name: formData.get('first_name'),
                last_name: formData.get('last_name'),
                email: formData.get('email')
            })
        });
        
        const data = await response.json();
        
        if (response.ok) {
            // Update stored user data
            localStorage.setItem('user_data', JSON.stringify(data.user));
            currentUser = data.user;
            updateUserDisplay();
            
            showNotification('Profile updated successfully!', 'success');
        } else {
            showNotification(data.error || 'Failed to update profile', 'error');
        }
        
    } catch (error) {
        console.error('Profile update error:', error);
        showNotification('Network error. Please try again.', 'error');
    } finally {
        setButtonLoading(submitButton, false);
    }
}

// Handle theme change
async function handleThemeChange(event) {
    const theme = event.target.value;
    
    // Apply theme immediately
    if (window.ThemeManager) {
        window.ThemeManager.applyTheme(theme);
    }
    
    // Update on server
    try {
        const token = localStorage.getItem('access_token');
        
        await fetch(`${API_BASE_URL}/user/preferences/theme`, {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ theme })
        });
        
    } catch (error) {
        console.error('Error updating theme preference:', error);
    }
}

// Handle notification preference change
async function handleNotificationChange(event) {
    const emailNotifications = event.target.checked;
    
    try {
        const token = localStorage.getItem('access_token');
        
        await fetch(`${API_BASE_URL}/user/preferences`, {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ email_notifications: emailNotifications })
        });
        
        showNotification('Notification preferences updated!', 'success');
        
    } catch (error) {
        console.error('Error updating notification preference:', error);
        showNotification('Failed to update preferences', 'error');
    }
}

// Utility functions
function setButtonLoading(button, isLoading) {
    const btnText = button.querySelector('.btn-text');
    const btnLoader = button.querySelector('.btn-loader');
    
    if (isLoading) {
        button.classList.add('loading');
        button.disabled = true;
        if (btnText) btnText.style.opacity = '0';
        if (btnLoader) btnLoader.style.display = 'block';
    } else {
        button.classList.remove('loading');
        button.disabled = false;
        if (btnText) btnText.style.opacity = '1';
        if (btnLoader) btnLoader.style.display = 'none';
    }
}

function showNotification(message, type = 'info') {
    const notification = document.getElementById('notification');
    
    notification.textContent = message;
    notification.className = `notification ${type}`;
    notification.classList.add('show');
    
    setTimeout(() => {
        notification.classList.remove('show');
    }, 5000);
}
