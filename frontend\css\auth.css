/* Authentication Page Styles */

.auth-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 1rem;
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-primary) 100%);
    position: relative;
}

.auth-card {
    width: 100%;
    max-width: 450px;
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    overflow: hidden;
    transition: all var(--transition-normal);
    animation: fadeIn 0.6s ease-out;
}

.auth-header {
    text-align: center;
    padding: 2rem 2rem 1rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    color: white;
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem;
    margin-bottom: 0.5rem;
}

.logo i {
    font-size: 2rem;
    color: white;
}

.logo h1 {
    font-size: 1.75rem;
    font-weight: 700;
    margin: 0;
    color: white;
}

.tagline {
    font-size: 0.875rem;
    opacity: 0.9;
    margin: 0;
    color: white;
}

.auth-form {
    padding: 2rem;
    display: none;
    animation: fadeIn 0.4s ease-out;
}

.auth-form.active {
    display: block;
}

.auth-form h2 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    text-align: center;
}

.form-subtitle {
    text-align: center;
    color: var(--text-secondary);
    margin-bottom: 2rem;
    font-size: 0.875rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.875rem;
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group i {
    position: absolute;
    left: 1rem;
    color: var(--text-muted);
    z-index: 1;
    font-size: 0.875rem;
}

.input-group input {
    width: 100%;
    padding: 0.875rem 1rem 0.875rem 2.75rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-md);
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: all var(--transition-fast);
}

.input-group input:focus {
    outline: none;
    border-color: var(--border-focus);
    box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1);
}

.input-group input.error {
    border-color: var(--border-error);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.password-toggle {
    position: absolute;
    right: 1rem;
    background: none;
    border: none;
    color: var(--text-muted);
    cursor: pointer;
    padding: 0.25rem;
    border-radius: var(--radius-sm);
    transition: color var(--transition-fast);
    z-index: 2;
}

.password-toggle:hover {
    color: var(--text-primary);
}

.password-requirements {
    margin-top: 0.5rem;
}

.password-requirements small {
    color: var(--text-muted);
    font-size: 0.75rem;
    line-height: 1.4;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    font-size: 0.875rem;
}

.checkbox-container {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    cursor: pointer;
}

.checkbox-container input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 1.125rem;
    height: 1.125rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all var(--transition-fast);
    flex-shrink: 0;
}

.checkbox-container input[type="checkbox"]:checked + .checkmark {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-container input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    color: white;
    font-size: 0.75rem;
    font-weight: bold;
}

.forgot-password {
    color: var(--primary-color);
    font-size: 0.875rem;
    text-decoration: none;
    transition: color var(--transition-fast);
}

.forgot-password:hover {
    color: var(--primary-hover);
    text-decoration: underline;
}

.btn {
    width: 100%;
    padding: 0.875rem 1.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    position: relative;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    color: white;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-primary:active {
    transform: translateY(0);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

.btn-text {
    transition: opacity var(--transition-fast);
}

.btn.loading .btn-text {
    opacity: 0;
}

.btn.loading .btn-loader {
    display: block !important;
}

.btn-loader {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: none;
}

.form-footer {
    text-align: center;
    padding-top: 1rem;
    border-top: 1px solid var(--border-color);
}

.form-footer p {
    margin: 0;
    color: var(--text-secondary);
    font-size: 0.875rem;
}

.form-footer a {
    color: var(--primary-color);
    font-weight: 500;
    text-decoration: none;
    transition: color var(--transition-fast);
}

.form-footer a:hover {
    color: var(--primary-hover);
    text-decoration: underline;
}

/* Form Row for Side-by-Side Inputs */
.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

/* Error States */
.form-group.error input {
    border-color: var(--border-error);
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.error-message {
    color: var(--danger-color);
    font-size: 0.75rem;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.error-message i {
    font-size: 0.75rem;
}

/* Success States */
.form-group.success input {
    border-color: var(--accent-color);
    box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

.success-message {
    color: var(--accent-color);
    font-size: 0.75rem;
    margin-top: 0.25rem;
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .auth-container {
        padding: 0.5rem;
    }
    
    .auth-card {
        max-width: 100%;
        margin: 0;
    }
    
    .auth-header {
        padding: 1.5rem 1.5rem 1rem;
    }
    
    .auth-form {
        padding: 1.5rem;
    }
    
    .form-row {
        grid-template-columns: 1fr;
        gap: 0;
    }
    
    .form-options {
        flex-direction: column;
        align-items: flex-start;
        gap: 1rem;
    }
    
    .logo h1 {
        font-size: 1.5rem;
    }
    
    .logo i {
        font-size: 1.75rem;
    }
}

@media (max-width: 480px) {
    .auth-header {
        padding: 1rem;
    }
    
    .auth-form {
        padding: 1rem;
    }
    
    .logo {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .logo h1 {
        font-size: 1.25rem;
    }
}
