<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - StockPredict</title>
    <link rel="stylesheet" href="css/styles.css">
    <link rel="stylesheet" href="css/dashboard.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation Header -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-brand">
                <i class="fas fa-chart-line"></i>
                <span>StockPredict</span>
            </div>
            
            <div class="nav-menu">
                <a href="#dashboard" class="nav-link active">
                    <i class="fas fa-tachometer-alt"></i>
                    Dashboard
                </a>
                <a href="#predict" class="nav-link">
                    <i class="fas fa-crystal-ball"></i>
                    Predict
                </a>
                <a href="#history" class="nav-link">
                    <i class="fas fa-history"></i>
                    History
                </a>
                <a href="#settings" class="nav-link">
                    <i class="fas fa-cog"></i>
                    Settings
                </a>
            </div>
            
            <div class="nav-actions">
                <!-- Theme Toggle -->
                <button id="themeToggle" class="theme-btn" title="Toggle theme">
                    <i class="fas fa-moon"></i>
                </button>
                
                <!-- User Menu -->
                <div class="user-menu">
                    <button class="user-btn" id="userMenuBtn">
                        <div class="user-avatar">
                            <i class="fas fa-user"></i>
                        </div>
                        <span class="user-name" id="userName">Loading...</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#profile" class="dropdown-item">
                            <i class="fas fa-user-circle"></i>
                            Profile
                        </a>
                        <a href="#settings" class="dropdown-item">
                            <i class="fas fa-cog"></i>
                            Settings
                        </a>
                        <div class="dropdown-divider"></div>
                        <button class="dropdown-item logout-btn" id="logoutBtn">
                            <i class="fas fa-sign-out-alt"></i>
                            Logout
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <!-- Dashboard Section -->
        <section id="dashboard-section" class="content-section active">
            <div class="container">
                <div class="page-header">
                    <h1>Dashboard</h1>
                    <p>Welcome back! Here's your stock prediction overview.</p>
                </div>
                
                <!-- Stats Cards -->
                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="totalPredictions">0</h3>
                            <p>Total Predictions</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon success">
                            <i class="fas fa-thumbs-up"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="accurateCount">0</h3>
                            <p>Accurate Predictions</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon warning">
                            <i class="fas fa-percentage"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="accuracyRate">0%</h3>
                            <p>Accuracy Rate</p>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <div class="stat-icon info">
                            <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="stat-content">
                            <h3 id="memberSince">-</h3>
                            <p>Member Since</p>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="quick-actions">
                    <h2>Quick Actions</h2>
                    <div class="action-grid">
                        <button class="action-card" onclick="showSection('predict')">
                            <i class="fas fa-magic"></i>
                            <h3>Make Prediction</h3>
                            <p>Predict stock movement for any symbol</p>
                        </button>
                        
                        <button class="action-card" onclick="showSection('history')">
                            <i class="fas fa-history"></i>
                            <h3>View History</h3>
                            <p>Check your past predictions</p>
                        </button>
                        
                        <button class="action-card" onclick="showSection('settings')">
                            <i class="fas fa-user-cog"></i>
                            <h3>Account Settings</h3>
                            <p>Manage your profile and preferences</p>
                        </button>
                    </div>
                </div>
                
                <!-- Recent Predictions -->
                <div class="recent-predictions">
                    <div class="section-header">
                        <h2>Recent Predictions</h2>
                        <button class="btn btn-secondary" onclick="showSection('history')">
                            View All
                        </button>
                    </div>
                    
                    <div class="predictions-list" id="recentPredictionsList">
                        <div class="loading-state">
                            <i class="fas fa-spinner fa-spin"></i>
                            <p>Loading recent predictions...</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Predict Section -->
        <section id="predict-section" class="content-section">
            <div class="container">
                <div class="page-header">
                    <h1>Stock Prediction</h1>
                    <p>Enter a stock symbol to get AI-powered movement predictions.</p>
                </div>
                
                <div class="predict-form-container">
                    <form id="predictForm" class="predict-form">
                        <div class="form-group">
                            <label for="stockSymbol">Stock Symbol</label>
                            <div class="input-group">
                                <i class="fas fa-search"></i>
                                <input type="text" id="stockSymbol" name="symbol" placeholder="e.g., AAPL, GOOGL, TSLA" required>
                                <button type="button" class="search-btn" id="searchBtn">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                            <div class="search-results" id="searchResults"></div>
                        </div>
                        
                        <div class="form-group">
                            <label for="predictionDate">Prediction Date</label>
                            <div class="input-group">
                                <i class="fas fa-calendar"></i>
                                <input type="date" id="predictionDate" name="prediction_date" required>
                            </div>
                            <small>Select a future date for prediction</small>
                        </div>
                        
                        <button type="submit" class="btn btn-primary">
                            <span class="btn-text">
                                <i class="fas fa-magic"></i>
                                Generate Prediction
                            </span>
                            <span class="btn-loader" style="display: none;">
                                <i class="fas fa-spinner fa-spin"></i>
                            </span>
                        </button>
                    </form>
                    
                    <!-- Prediction Result -->
                    <div class="prediction-result" id="predictionResult" style="display: none;">
                        <div class="result-header">
                            <h3>Prediction Result</h3>
                            <div class="result-symbol" id="resultSymbol"></div>
                        </div>
                        
                        <div class="result-content">
                            <div class="prediction-direction" id="predictionDirection">
                                <div class="direction-icon"></div>
                                <div class="direction-text"></div>
                            </div>
                            
                            <div class="confidence-score">
                                <label>Confidence Score</label>
                                <div class="confidence-bar">
                                    <div class="confidence-fill" id="confidenceFill"></div>
                                    <span class="confidence-text" id="confidenceText">0%</span>
                                </div>
                            </div>
                            
                            <div class="prediction-reasoning" id="predictionReasoning">
                                <h4>Analysis</h4>
                                <p></p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- History Section -->
        <section id="history-section" class="content-section">
            <div class="container">
                <div class="page-header">
                    <h1>Prediction History</h1>
                    <p>Review your past stock predictions and their outcomes.</p>
                </div>
                
                <div class="history-filters">
                    <div class="filter-group">
                        <label for="filterSymbol">Filter by Symbol</label>
                        <input type="text" id="filterSymbol" placeholder="Enter stock symbol">
                    </div>
                    
                    <div class="filter-group">
                        <label for="filterDate">Filter by Date</label>
                        <input type="date" id="filterDate">
                    </div>
                    
                    <button class="btn btn-secondary" id="clearFilters">
                        Clear Filters
                    </button>
                </div>
                
                <div class="history-list" id="historyList">
                    <div class="loading-state">
                        <i class="fas fa-spinner fa-spin"></i>
                        <p>Loading prediction history...</p>
                    </div>
                </div>
                
                <div class="pagination" id="historyPagination" style="display: none;">
                    <button class="btn btn-secondary" id="prevPage">Previous</button>
                    <span class="page-info" id="pageInfo">Page 1 of 1</span>
                    <button class="btn btn-secondary" id="nextPage">Next</button>
                </div>
            </div>
        </section>

        <!-- Settings Section -->
        <section id="settings-section" class="content-section">
            <div class="container">
                <div class="page-header">
                    <h1>Settings</h1>
                    <p>Manage your account and preferences.</p>
                </div>
                
                <div class="settings-grid">
                    <!-- Profile Settings -->
                    <div class="settings-card">
                        <h3>Profile Information</h3>
                        <form id="profileForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="profileFirstName">First Name</label>
                                    <input type="text" id="profileFirstName" name="first_name">
                                </div>
                                <div class="form-group">
                                    <label for="profileLastName">Last Name</label>
                                    <input type="text" id="profileLastName" name="last_name">
                                </div>
                            </div>
                            
                            <div class="form-group">
                                <label for="profileEmail">Email</label>
                                <input type="email" id="profileEmail" name="email" required>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                Update Profile
                            </button>
                        </form>
                    </div>
                    
                    <!-- Theme Preferences -->
                    <div class="settings-card">
                        <h3>Appearance</h3>
                        <div class="preference-group">
                            <label>Theme</label>
                            <div class="theme-options">
                                <label class="radio-option">
                                    <input type="radio" name="theme" value="light" id="themeLight">
                                    <span class="radio-custom"></span>
                                    Light Mode
                                </label>
                                <label class="radio-option">
                                    <input type="radio" name="theme" value="dark" id="themeDark">
                                    <span class="radio-custom"></span>
                                    Dark Mode
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Notification Settings -->
                    <div class="settings-card">
                        <h3>Notifications</h3>
                        <div class="preference-group">
                            <label class="checkbox-container">
                                <input type="checkbox" id="emailNotifications">
                                <span class="checkmark"></span>
                                Email Notifications
                            </label>
                            <small>Receive email updates about your predictions</small>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Notification -->
    <div id="notification" class="notification"></div>

    <!-- Scripts -->
    <script src="js/theme.js"></script>
    <script src="js/dashboard.js"></script>
</body>
</html>
