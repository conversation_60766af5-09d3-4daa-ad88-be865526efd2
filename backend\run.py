#!/usr/bin/env python3
"""
Main application entry point for the Stock Prediction Website.
"""

import os
import sys

# Add the parent directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from flask import Flask
from backend.app import create_app, db
from backend.app.models.user import User, UserPreference
from backend.app.models.prediction import StockPrediction, StockData

def create_application():
    """Create and configure the Flask application."""
    config_name = os.getenv('FLASK_ENV', 'development')
    app = create_app(config_name)
    return app

def init_database(app):
    """Initialize the database with tables."""
    with app.app_context():
        try:
            # Create all tables
            db.create_all()
            print("✅ Database tables created successfully!")
            
            # Check if admin user exists, if not create one
            admin_user = User.query.filter_by(username='admin').first()
            if not admin_user:
                admin_user = User(
                    username='admin',
                    email='<EMAIL>',
                    password='Admin123!',
                    first_name='Admin',
                    last_name='User'
                )
                db.session.add(admin_user)
                db.session.flush()
                
                # Create admin preferences
                admin_preferences = UserPreference(user_id=admin_user.id)
                db.session.add(admin_preferences)
                
                db.session.commit()
                print("✅ Admin user created successfully!")
                print("   Username: admin")
                print("   Password: Admin123!")
            else:
                print("ℹ️  Admin user already exists")
                
        except Exception as e:
            print(f"❌ Error initializing database: {e}")
            db.session.rollback()

def main():
    """Main function to run the application."""
    app = create_application()
    
    # Handle command line arguments
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == 'init-db':
            print("🔄 Initializing database...")
            init_database(app)
            return
        
        elif command == 'create-admin':
            print("🔄 Creating admin user...")
            with app.app_context():
                try:
                    admin_user = User.query.filter_by(username='admin').first()
                    if admin_user:
                        print("ℹ️  Admin user already exists")
                    else:
                        admin_user = User(
                            username='admin',
                            email='<EMAIL>',
                            password='Admin123!',
                            first_name='Admin',
                            last_name='User'
                        )
                        db.session.add(admin_user)
                        db.session.flush()
                        
                        admin_preferences = UserPreference(user_id=admin_user.id)
                        db.session.add(admin_preferences)
                        
                        db.session.commit()
                        print("✅ Admin user created successfully!")
                        print("   Username: admin")
                        print("   Password: Admin123!")
                except Exception as e:
                    print(f"❌ Error creating admin user: {e}")
                    db.session.rollback()
            return
        
        elif command == 'shell':
            print("🔄 Starting Flask shell...")
            with app.app_context():
                import code
                code.interact(local=dict(globals(), **locals()))
            return
    
    # Initialize database on first run
    with app.app_context():
        try:
            # Check if tables exist
            db.create_all()
        except Exception as e:
            print(f"❌ Database error: {e}")
    
    # Run the application
    print("🚀 Starting Stock Prediction Website...")
    print(f"🌐 Running on: http://127.0.0.1:5000")
    print(f"🔧 Environment: {os.getenv('FLASK_ENV', 'development')}")
    print("📊 API Documentation:")
    print("   - POST /api/auth/register - User registration")
    print("   - POST /api/auth/login - User login")
    print("   - GET /api/user/preferences - Get user preferences")
    print("   - PUT /api/user/preferences - Update user preferences")
    print("   - POST /api/predict/stock - Get stock prediction")
    print("\n💡 Press Ctrl+C to stop the server")
    
    try:
        app.run(
            host='0.0.0.0',
            port=5000,
            debug=app.config.get('DEBUG', False)
        )
    except KeyboardInterrupt:
        print("\n👋 Server stopped by user")

if __name__ == '__main__':
    main()
