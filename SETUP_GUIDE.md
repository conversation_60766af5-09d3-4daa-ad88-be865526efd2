# Stock Prediction Website - Complete Setup Guide

This guide will walk you through setting up your stock prediction website with user authentication and theme preferences.

## 🚀 Quick Start (Recommended)

### Step 1: Install Python Dependencies
```bash
pip install -r requirements.txt
```

### Step 2: Start the Application
```bash
python start_server.py
```

That's it! The application will:
- ✅ Start the backend API server
- ✅ Start the frontend web server  
- ✅ Initialize the database
- ✅ Open your browser automatically

## 📋 What You Get

### Core Features Implemented:

#### 🔐 User Authentication System
- **Registration**: Create new accounts with email validation
- **Login/Logout**: Secure JWT-based authentication
- **Password Security**: Strong password requirements
- **Profile Management**: Update user information

#### 🎨 Theme System
- **Dark/Light Mode**: Toggle between themes
- **User Preferences**: Saved per user account
- **Smooth Transitions**: Animated theme switching
- **System Detection**: Auto-detect system theme preference

#### 📈 Stock Prediction Engine
- **AI-Powered Predictions**: Machine learning model for stock movement
- **Real-Time Data**: Fetches current stock information
- **Confidence Scoring**: Shows prediction reliability
- **History Tracking**: View past predictions

#### 📊 User Dashboard
- **Statistics Overview**: Track your prediction performance
- **Recent Activity**: See your latest predictions
- **Quick Actions**: Easy access to main features
- **Responsive Design**: Works on all devices

## 🛠️ Manual Setup (Advanced)

If you prefer to set up components individually:

### Backend Setup
```bash
# Initialize database
python backend/run.py init-db

# Start backend server
python backend/run.py
```

### Frontend Setup
```bash
# Serve frontend files (in another terminal)
cd frontend
python -m http.server 3000
```

### ML Model Training (Optional)
```bash
# Train the prediction model
python train_model.py
```

## 🧪 Testing Your Setup

Run the comprehensive test suite:
```bash
python test_setup.py
```

This will verify:
- ✅ Backend server is running
- ✅ User registration works
- ✅ Login functionality works
- ✅ Theme preferences save correctly
- ✅ Stock predictions generate
- ✅ All frontend files are present

## 🌐 Using the Website

### First Time User Experience:

1. **Registration**
   - Open http://127.0.0.1:3000
   - Click "Sign up" to create an account
   - Fill in your details (first name, last name optional)
   - Choose a strong password

2. **Dashboard Overview**
   - View your account statistics
   - See recent predictions (empty initially)
   - Access quick action buttons

3. **Making Predictions**
   - Click "Make Prediction" or go to Predict section
   - Enter a stock symbol (e.g., AAPL, GOOGL, TSLA)
   - Select a future date
   - Get AI-powered prediction with confidence score

4. **Customizing Experience**
   - Toggle dark/light theme using the moon/sun button
   - Go to Settings to update profile and preferences
   - Enable/disable email notifications

## 🎯 Key Features Demonstrated

### Authentication Flow
```
Registration → Email Validation → Secure Login → JWT Token → Protected Routes
```

### Theme System
```
User Preference → Local Storage → Server Sync → CSS Variables → Smooth Transitions
```

### Prediction Pipeline
```
Stock Symbol → Data Fetch → ML Model → Confidence Score → Database Storage
```

## 📁 Project Structure

```
stock-prediction-website/
├── 🔧 Backend (Flask API)
│   ├── app/
│   │   ├── models/         # Database models (User, Preferences, Predictions)
│   │   ├── routes/         # API endpoints (auth, user, prediction)
│   │   └── __init__.py     # App factory
│   ├── ml_models/          # Machine learning components
│   └── run.py              # Server startup
├── 🎨 Frontend (HTML/CSS/JS)
│   ├── css/                # Stylesheets with theme support
│   ├── js/                 # JavaScript for auth, theme, dashboard
│   ├── index.html          # Login/registration page
│   └── dashboard.html      # Main application interface
├── 📊 ML & Data
│   ├── train_model.py      # Model training script
│   └── requirements.txt    # Python dependencies
└── 🚀 Utilities
    ├── start_server.py     # Easy startup script
    ├── test_setup.py       # Comprehensive testing
    └── README.md           # Documentation
```

## 🔧 Configuration Options

### Environment Variables (.env)
```bash
# Flask settings
FLASK_ENV=development
SECRET_KEY=your-secret-key
JWT_SECRET_KEY=your-jwt-secret

# Database
DATABASE_URL=sqlite:///stock_prediction.db

# API Keys (optional)
ALPHA_VANTAGE_API_KEY=your-key
```

### Database Models
- **Users**: Authentication and profile data
- **UserPreferences**: Theme and notification settings  
- **StockPredictions**: Prediction history and results
- **StockData**: Historical stock information

## 🎨 Theme System Details

### CSS Variables
The theme system uses CSS custom properties for easy switching:
```css
:root {
  --primary-color: #2563eb;
  --bg-primary: #ffffff;
  --text-primary: #1e293b;
}

[data-theme="dark"] {
  --bg-primary: #0f172a;
  --text-primary: #f8fafc;
}
```

### JavaScript Theme Manager
- Detects system preference
- Saves user choice to localStorage
- Syncs with server for logged-in users
- Provides smooth transitions

## 🤖 ML Model Information

### Features Used
- Moving averages (5, 10, 20, 50 day)
- RSI (Relative Strength Index)
- MACD (Moving Average Convergence Divergence)
- Bollinger Bands
- Volume indicators
- Price momentum

### Model Details
- **Algorithm**: Random Forest Classifier
- **Training Data**: Multiple popular stocks (AAPL, GOOGL, etc.)
- **Prediction**: Binary classification (up/down)
- **Confidence**: Probability score from the model

## 🚨 Troubleshooting

### Common Issues:

**"Module not found" errors:**
```bash
pip install -r requirements.txt
```

**Port already in use:**
- Stop other servers using ports 5000 or 3000
- Or modify the ports in the scripts

**Database errors:**
```bash
python backend/run.py init-db
```

**Prediction failures:**
- Check internet connection (needed for stock data)
- Try training the model: `python train_model.py`

**Theme not saving:**
- Ensure you're logged in
- Check browser console for errors

## 🎉 Success Indicators

You'll know everything is working when:
- ✅ You can register and login successfully
- ✅ Theme toggle works and persists
- ✅ Stock predictions generate with confidence scores
- ✅ Dashboard shows your statistics
- ✅ Settings save your preferences

## 📞 Getting Help

If you encounter issues:
1. Run `python test_setup.py` for diagnostics
2. Check console output for error messages
3. Ensure all files are present in the project directory
4. Verify Python version (3.7+ recommended)

## 🎯 Next Steps

Your stock prediction website is now ready! You can:
- Add more sophisticated ML features
- Implement additional stock analysis tools
- Add email notifications
- Deploy to a cloud platform
- Integrate with more data sources

Enjoy your new stock prediction platform! 🚀📈
