#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to train the stock prediction ML model.
This script can be run independently to train and save the model.
"""

import os
import sys
from datetime import datetime

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def train_model():
    """Train the stock prediction model."""
    print("🚀 Stock Prediction Model Training")
    print("=" * 40)
    
    try:
        from backend.ml_models.stock_predictor import StockPredictor
        
        # Create predictor instance
        predictor = StockPredictor()
        
        # Define training symbols (popular stocks)
        training_symbols = [
            'AAPL',  # Apple
            'GOOGL', # Alphabet
            'MSFT',  # Microsoft
            'TSLA',  # Tesla
            'AMZN',  # Amazon
            'META',  # Meta
            'NVDA',  # NVIDIA
            'JPM',   # JPMorgan Chase
            'JNJ',   # Johnson & Johnson
            'V'      # Visa
        ]
        
        print(f"📊 Training on {len(training_symbols)} stocks:")
        for symbol in training_symbols:
            print(f"   - {symbol}")
        
        print("\n🔄 Starting training process...")
        
        # Train the model
        results = predictor.train_model(symbols=training_symbols, test_size=0.2)
        
        # Save the trained model
        print("\n💾 Saving trained model...")
        if predictor.save_model():
            print("✅ Model saved successfully!")
        else:
            print("❌ Failed to save model")
        
        # Display results
        print("\n📈 Training Results:")
        print(f"   Training Accuracy: {results['train_accuracy']:.3f}")
        print(f"   Test Accuracy: {results['test_accuracy']:.3f}")
        print(f"   Training Samples: {results['training_samples']:,}")
        print(f"   Test Samples: {results['test_samples']:,}")
        print(f"   Features Used: {len(results['features_used'])}")
        
        print("\n🔍 Feature Importance:")
        for i, feature in enumerate(results['features_used'][:5]):
            print(f"   {i+1}. {feature}")
        
        # Test prediction on a sample stock
        print("\n🧪 Testing prediction on AAPL...")
        test_result = predictor.predict_stock_movement('AAPL')
        
        if test_result.get('success'):
            print(f"   Symbol: {test_result['symbol']}")
            print(f"   Prediction: {test_result['predicted_direction'].upper()}")
            print(f"   Confidence: {test_result['confidence_score']:.2f}")
            print(f"   Latest Price: ${test_result['latest_price']:.2f}")
        else:
            print(f"   Test prediction failed: {test_result.get('error')}")
        
        print("\n🎉 Model training completed successfully!")
        print("\n💡 The trained model will now be used for predictions in the web application.")
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("   Make sure all dependencies are installed: pip install -r requirements.txt")
    except Exception as e:
        print(f"❌ Training failed: {e}")
        import traceback
        traceback.print_exc()

def test_model():
    """Test the trained model with sample predictions."""
    print("\n🧪 Testing Trained Model")
    print("=" * 30)
    
    try:
        from backend.ml_models.stock_predictor import StockPredictor
        
        predictor = StockPredictor()
        
        # Try to load existing model
        if not predictor.load_model():
            print("❌ No trained model found. Please run training first.")
            return
        
        # Test symbols
        test_symbols = ['AAPL', 'GOOGL', 'TSLA', 'MSFT']
        
        print(f"Testing predictions for {len(test_symbols)} stocks:\n")
        
        for symbol in test_symbols:
            print(f"🔍 {symbol}:")
            result = predictor.predict_stock_movement(symbol)
            
            if result.get('success'):
                direction_emoji = "📈" if result['predicted_direction'] == 'up' else "📉"
                print(f"   {direction_emoji} Prediction: {result['predicted_direction'].upper()}")
                print(f"   🎯 Confidence: {result['confidence_score']:.2f}")
                print(f"   💰 Latest Price: ${result['latest_price']:.2f}")
                
                if result.get('top_features'):
                    print(f"   📊 Top Features: {', '.join([f[0] for f in result['top_features'][:2]])}")
            else:
                print(f"   ❌ Prediction failed: {result.get('error')}")
            
            print()
        
        print("✅ Model testing completed!")
        
    except Exception as e:
        print(f"❌ Testing failed: {e}")

def main():
    """Main function."""
    if len(sys.argv) > 1:
        command = sys.argv[1].lower()
        
        if command == 'test':
            test_model()
        elif command == 'train':
            train_model()
        else:
            print("Usage: python train_model.py [train|test]")
            print("  train - Train a new model")
            print("  test  - Test existing model")
    else:
        # Default action is to train
        train_model()

if __name__ == '__main__':
    main()
