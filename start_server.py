#!/usr/bin/env python3
"""
Simple server starter script for the Stock Prediction Website.
This script starts both the backend API server and serves the frontend files.
"""

import os
import sys
import threading
import time
import webbrowser
from http.server import HTTPServer, SimpleHTTPRequestHandler
import socketserver

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

class FrontendHandler(SimpleHTTPRequestHandler):
    """Custom handler to serve frontend files with proper MIME types."""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory='frontend', **kwargs)
    
    def end_headers(self):
        # Add CORS headers for development
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        super().end_headers()
    
    def do_OPTIONS(self):
        # Handle preflight requests
        self.send_response(200)
        self.end_headers()

def start_backend_server():
    """Start the Flask backend server."""
    print("🔄 Starting backend server...")
    
    try:
        from backend.run import main as run_backend
        run_backend()
    except Exception as e:
        print(f"❌ Error starting backend server: {e}")
        print("   Make sure all dependencies are installed: pip install -r requirements.txt")

def start_frontend_server():
    """Start the frontend file server."""
    print("🔄 Starting frontend server...")
    
    try:
        # Wait a moment for backend to start
        time.sleep(2)
        
        PORT = 3000
        
        with socketserver.TCPServer(("", PORT), FrontendHandler) as httpd:
            print(f"✅ Frontend server running at http://127.0.0.1:{PORT}")
            print(f"📁 Serving files from: {os.path.abspath('frontend')}")
            
            # Open browser after a short delay
            def open_browser():
                time.sleep(3)
                try:
                    webbrowser.open(f'http://127.0.0.1:{PORT}')
                    print("🌐 Opened website in your default browser")
                except Exception as e:
                    print(f"Could not open browser automatically: {e}")
                    print(f"Please manually open: http://127.0.0.1:{PORT}")
            
            browser_thread = threading.Thread(target=open_browser, daemon=True)
            browser_thread.start()
            
            httpd.serve_forever()
            
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"❌ Port 3000 is already in use. Please stop other servers or use a different port.")
        else:
            print(f"❌ Error starting frontend server: {e}")
    except KeyboardInterrupt:
        print("\n👋 Frontend server stopped by user")
    except Exception as e:
        print(f"❌ Error starting frontend server: {e}")

def check_requirements():
    """Check if required files and dependencies exist."""
    print("🔍 Checking requirements...")
    
    # Check if requirements.txt exists
    if not os.path.exists('requirements.txt'):
        print("❌ requirements.txt not found")
        return False
    
    # Check if frontend directory exists
    if not os.path.exists('frontend'):
        print("❌ frontend directory not found")
        return False
    
    # Check if backend directory exists
    if not os.path.exists('backend'):
        print("❌ backend directory not found")
        return False
    
    # Check key frontend files
    key_files = ['frontend/index.html', 'frontend/dashboard.html']
    for file_path in key_files:
        if not os.path.exists(file_path):
            print(f"❌ {file_path} not found")
            return False
    
    print("✅ All required files found")
    return True

def main():
    """Main function to start both servers."""
    print("🚀 Stock Prediction Website Launcher")
    print("=" * 40)
    
    # Check requirements
    if not check_requirements():
        print("\n❌ Missing required files. Please ensure the project is set up correctly.")
        return
    
    print("\n📋 Starting servers...")
    print("   - Backend API: http://127.0.0.1:5000")
    print("   - Frontend: http://127.0.0.1:3000")
    print("\n💡 Press Ctrl+C to stop both servers")
    
    try:
        # Start backend server in a separate thread
        backend_thread = threading.Thread(target=start_backend_server, daemon=True)
        backend_thread.start()
        
        # Start frontend server in main thread
        start_frontend_server()
        
    except KeyboardInterrupt:
        print("\n\n👋 Servers stopped by user")
        print("Thank you for using Stock Prediction Website!")
    except Exception as e:
        print(f"\n❌ Error: {e}")

if __name__ == '__main__':
    main()
