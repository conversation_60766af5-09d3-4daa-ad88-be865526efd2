from flask import Blueprint, request, jsonify
from flask_jwt_extended import jwt_required, get_jwt_identity
from backend.app import db
from backend.app.models.user import User
from backend.app.models.prediction import StockPrediction, StockData
from datetime import datetime, date
import yfinance as yf
import json

prediction_bp = Blueprint('prediction', __name__)

@prediction_bp.route('/stock', methods=['POST'])
@jwt_required()
def predict_stock():
    """Predict stock movement for a given symbol and date."""
    try:
        current_user_id = get_jwt_identity()
        user = User.query.get(current_user_id)
        
        if not user:
            return jsonify({'error': 'User not found'}), 404
        
        data = request.get_json()
        
        # Validate required fields
        if not data.get('symbol'):
            return jsonify({'error': 'Stock symbol is required'}), 400
        
        symbol = data['symbol'].upper().strip()
        prediction_date = data.get('prediction_date', date.today().isoformat())
        
        # Parse prediction date
        try:
            if isinstance(prediction_date, str):
                prediction_date = datetime.strptime(prediction_date, '%Y-%m-%d').date()
        except ValueError:
            return jsonify({'error': 'Invalid date format. Use YYYY-MM-DD'}), 400
        
        # Basic validation for stock symbol
        if len(symbol) > 10 or not symbol.isalpha():
            return jsonify({'error': 'Invalid stock symbol'}), 400
        
        # Generate prediction using ML model or fallback to mock
        prediction_result = _generate_prediction(symbol, prediction_date.isoformat())
        
        # Save prediction to database
        prediction = StockPrediction(
            user_id=current_user_id,
            stock_symbol=symbol,
            prediction_date=prediction_date,
            predicted_direction=prediction_result['direction'],
            confidence_score=prediction_result['confidence'],
            model_version='v1.0',
            features_used=json.dumps(prediction_result['features'])
        )
        
        db.session.add(prediction)
        db.session.commit()
        
        return jsonify({
            'message': 'Prediction generated successfully',
            'prediction': prediction.to_dict(),
            'details': {
                'symbol': symbol,
                'predicted_direction': prediction_result['direction'],
                'confidence_score': prediction_result['confidence'],
                'reasoning': prediction_result['reasoning']
            }
        }), 200
        
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': 'Failed to generate prediction', 'details': str(e)}), 500

@prediction_bp.route('/history', methods=['GET'])
@jwt_required()
def get_prediction_history():
    """Get user's prediction history."""
    try:
        current_user_id = get_jwt_identity()
        
        # Get query parameters
        page = request.args.get('page', 1, type=int)
        per_page = min(request.args.get('per_page', 10, type=int), 50)  # Max 50 per page
        symbol = request.args.get('symbol', '').upper()
        
        # Build query
        query = StockPrediction.query.filter_by(user_id=current_user_id)
        
        if symbol:
            query = query.filter_by(stock_symbol=symbol)
        
        # Order by most recent first
        query = query.order_by(StockPrediction.created_at.desc())
        
        # Paginate
        predictions = query.paginate(
            page=page, 
            per_page=per_page, 
            error_out=False
        )
        
        return jsonify({
            'predictions': [pred.to_dict() for pred in predictions.items],
            'pagination': {
                'page': page,
                'per_page': per_page,
                'total': predictions.total,
                'pages': predictions.pages,
                'has_next': predictions.has_next,
                'has_prev': predictions.has_prev
            }
        }), 200
        
    except Exception as e:
        return jsonify({'error': 'Failed to get prediction history', 'details': str(e)}), 500

@prediction_bp.route('/stocks/search', methods=['GET'])
@jwt_required()
def search_stocks():
    """Search for stock symbols (mock implementation)."""
    try:
        query = request.args.get('q', '').upper().strip()
        
        if not query or len(query) < 1:
            return jsonify({'error': 'Search query is required'}), 400
        
        # Mock stock data - in production, you'd use a real stock API
        mock_stocks = [
            {'symbol': 'AAPL', 'name': 'Apple Inc.', 'exchange': 'NASDAQ'},
            {'symbol': 'GOOGL', 'name': 'Alphabet Inc.', 'exchange': 'NASDAQ'},
            {'symbol': 'MSFT', 'name': 'Microsoft Corporation', 'exchange': 'NASDAQ'},
            {'symbol': 'TSLA', 'name': 'Tesla, Inc.', 'exchange': 'NASDAQ'},
            {'symbol': 'AMZN', 'name': 'Amazon.com, Inc.', 'exchange': 'NASDAQ'},
            {'symbol': 'META', 'name': 'Meta Platforms, Inc.', 'exchange': 'NASDAQ'},
            {'symbol': 'NVDA', 'name': 'NVIDIA Corporation', 'exchange': 'NASDAQ'},
            {'symbol': 'JPM', 'name': 'JPMorgan Chase & Co.', 'exchange': 'NYSE'},
            {'symbol': 'JNJ', 'name': 'Johnson & Johnson', 'exchange': 'NYSE'},
            {'symbol': 'V', 'name': 'Visa Inc.', 'exchange': 'NYSE'}
        ]
        
        # Filter stocks based on query
        filtered_stocks = [
            stock for stock in mock_stocks 
            if query in stock['symbol'] or query in stock['name'].upper()
        ]
        
        return jsonify({
            'stocks': filtered_stocks[:10],  # Limit to 10 results
            'query': query
        }), 200
        
    except Exception as e:
        return jsonify({'error': 'Failed to search stocks', 'details': str(e)}), 500

@prediction_bp.route('/stocks/<symbol>/info', methods=['GET'])
@jwt_required()
def get_stock_info(symbol):
    """Get basic stock information."""
    try:
        symbol = symbol.upper().strip()
        
        # Try to get real stock data using yfinance
        try:
            stock = yf.Ticker(symbol)
            info = stock.info
            
            stock_info = {
                'symbol': symbol,
                'name': info.get('longName', 'N/A'),
                'current_price': info.get('currentPrice', 0),
                'previous_close': info.get('previousClose', 0),
                'market_cap': info.get('marketCap', 0),
                'volume': info.get('volume', 0),
                'exchange': info.get('exchange', 'N/A'),
                'currency': info.get('currency', 'USD'),
                'sector': info.get('sector', 'N/A'),
                'industry': info.get('industry', 'N/A')
            }
            
        except Exception:
            # Fallback to mock data if yfinance fails
            stock_info = {
                'symbol': symbol,
                'name': f'{symbol} Corporation',
                'current_price': 150.00,
                'previous_close': 148.50,
                'market_cap': 1000000000,
                'volume': 1000000,
                'exchange': 'NASDAQ',
                'currency': 'USD',
                'sector': 'Technology',
                'industry': 'Software'
            }
        
        return jsonify({
            'stock_info': stock_info
        }), 200
        
    except Exception as e:
        return jsonify({'error': 'Failed to get stock info', 'details': str(e)}), 500

def _generate_prediction(symbol, prediction_date):
    """Generate a prediction using the ML model or fallback to mock."""
    try:
        # Try to use the ML model
        from backend.ml_models.stock_predictor import get_predictor

        predictor = get_predictor()
        result = predictor.predict_stock_movement(symbol, prediction_date)

        if result.get('success'):
            return {
                'direction': result['predicted_direction'],
                'confidence': result['confidence_score'],
                'features': [f[0] for f in result.get('top_features', [])],
                'reasoning': f"ML model prediction based on technical analysis. Top indicators: {', '.join([f[0] for f in result.get('top_features', [])][:3])}"
            }
        else:
            # Fallback to mock if ML model fails
            return _generate_mock_prediction(symbol)

    except Exception as e:
        print(f"ML prediction failed for {symbol}: {e}")
        # Fallback to mock prediction
        return _generate_mock_prediction(symbol)

def _generate_mock_prediction(symbol):
    """Generate a mock prediction for demonstration purposes."""
    import random

    # Simple mock prediction logic
    directions = ['up', 'down']
    direction = random.choice(directions)
    confidence = round(random.uniform(0.6, 0.95), 2)

    features = [
        'moving_average_20',
        'moving_average_50',
        'rsi',
        'volume_trend',
        'price_momentum'
    ]

    reasoning = {
        'up': 'Technical indicators suggest bullish momentum with strong volume support.',
        'down': 'Bearish signals detected with declining momentum and weak volume.'
    }

    return {
        'direction': direction,
        'confidence': confidence,
        'features': features,
        'reasoning': reasoning[direction]
    }
