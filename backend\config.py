import os
from datetime import timedelta
from decouple import config

class Config:
    """Base configuration class."""
    SECRET_KEY = config('SECRET_KEY', default='dev-secret-key-change-in-production')
    JWT_SECRET_KEY = config('JWT_SECRET_KEY', default='jwt-secret-key-change-in-production')
    JWT_ACCESS_TOKEN_EXPIRES = timedelta(hours=24)
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_DATABASE_URI = config('DATABASE_URL', default='sqlite:///stock_prediction.db')
    
    # CORS settings
    CORS_ORIGINS = ['http://localhost:3000', 'http://127.0.0.1:3000', 'http://localhost:5000']
    
    # API settings
    ALPHA_VANTAGE_API_KEY = config('ALPHA_VANTAGE_API_KEY', default='')
    YAHOO_FINANCE_API_KEY = config('YAHOO_FINANCE_API_KEY', default='')

class DevelopmentConfig(Config):
    """Development configuration."""
    DEBUG = True
    TESTING = False

class ProductionConfig(Config):
    """Production configuration."""
    DEBUG = False
    TESTING = False

class TestingConfig(Config):
    """Testing configuration."""
    DEBUG = True
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///test_stock_prediction.db'

# Configuration dictionary
config_dict = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
