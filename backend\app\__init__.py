from flask import Flask
from flask_sqlalchemy import SQLAlchemy
from flask_jwt_extended import J<PERSON><PERSON>anager
from flask_bcrypt import Bcrypt
from flask_cors import CORS
from backend.config import config_dict

# Initialize extensions
db = SQLAlchemy()
jwt = JWTManager()
bcrypt = Bcrypt()
cors = CORS()

def create_app(config_name='default'):
    """Application factory pattern."""
    app = Flask(__name__)
    
    # Load configuration
    app.config.from_object(config_dict[config_name])
    
    # Initialize extensions with app
    db.init_app(app)
    jwt.init_app(app)
    bcrypt.init_app(app)
    cors.init_app(app, origins=app.config['CORS_ORIGINS'])
    
    # Register blueprints
    try:
        from backend.app.routes.auth import auth_bp
        from backend.app.routes.user import user_bp
        from backend.app.routes.prediction import prediction_bp

        app.register_blueprint(auth_bp, url_prefix='/api/auth')
        app.register_blueprint(user_bp, url_prefix='/api/user')
        app.register_blueprint(prediction_bp, url_prefix='/api/predict')
    except ImportError as e:
        print(f"Warning: Could not import routes: {e}")
        # Create minimal routes for testing
        @app.route('/api/health')
        def health_check():
            return {'status': 'ok', 'message': 'Server is running'}
    
    # Create database tables
    with app.app_context():
        db.create_all()
    
    return app
